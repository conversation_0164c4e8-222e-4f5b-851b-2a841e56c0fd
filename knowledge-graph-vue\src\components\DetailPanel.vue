<template>
  <div class="detail-panel">
    <div class="panel-header">
      <h3 class="panel-title">
        {{ type === 'node' ? '🔵 节点详情' : '🔗 边详情' }}
      </h3>
      <button 
        class="close-btn"
        @click="$emit('close')"
        title="关闭详情面板"
      >
        ✕
      </button>
    </div>
    
    <div class="panel-content">
      <!-- 节点详情 -->
      <div v-if="type === 'node' && data" class="node-details">
        <div class="detail-section">
          <div class="detail-item">
            <span class="detail-label">ID:</span>
            <span class="detail-value">{{ data.id }}</span>
          </div>
          
          <div class="detail-item">
            <span class="detail-label">标题:</span>
            <span class="detail-value">{{ data.title || '无标题' }}</span>
          </div>
          
          <div class="detail-item">
            <span class="detail-label">类型:</span>
            <span class="detail-value type-badge" :style="{ backgroundColor: getTypeColor(data.type) }">
              {{ data.type || '未分类' }}
            </span>
          </div>
          
          <div class="detail-item">
            <span class="detail-label">度数:</span>
            <span class="detail-value degree-value" :style="{ color: getDegreeColor(data.degree) }">
              {{ data.degree }}
            </span>
          </div>
          
          <div class="detail-item">
            <span class="detail-label">频率:</span>
            <span class="detail-value">{{ data.frequency || 0 }}</span>
          </div>
        </div>
        
        <div v-if="data.description" class="detail-section">
          <div class="detail-label">描述:</div>
          <div class="description-content">
            {{ data.description }}
          </div>
        </div>
        
        <!-- 连接信息 -->
        <div class="detail-section">
          <div class="detail-label">连接信息:</div>
          <div class="connections-info">
            <div class="connection-item">
              <span class="connection-label">入度:</span>
              <span class="connection-value">{{ getInDegree(data.id) }}</span>
            </div>
            <div class="connection-item">
              <span class="connection-label">出度:</span>
              <span class="connection-value">{{ getOutDegree(data.id) }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 边详情 -->
      <div v-else-if="type === 'edge' && data" class="edge-details">
        <div class="detail-section">
          <div class="detail-item">
            <span class="detail-label">ID:</span>
            <span class="detail-value">{{ data.id }}</span>
          </div>
          
          <div class="detail-item">
            <span class="detail-label">源节点:</span>
            <span class="detail-value node-link" @click="selectNode(data.source)">
              {{ data.from || data.source }}
            </span>
          </div>
          
          <div class="detail-item">
            <span class="detail-label">目标节点:</span>
            <span class="detail-value node-link" @click="selectNode(data.target)">
              {{ data.to || data.target }}
            </span>
          </div>
          
          <div class="detail-item">
            <span class="detail-label">权重:</span>
            <span class="detail-value weight-value">{{ data.weight || 0 }}</span>
          </div>
          
          <div class="detail-item">
            <span class="detail-label">组合度数:</span>
            <span class="detail-value">{{ data.combined_degree || 0 }}</span>
          </div>
        </div>
        
        <div v-if="data.description" class="detail-section">
          <div class="detail-label">描述:</div>
          <div class="description-content">
            {{ data.description }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useGraphDataStore } from '../stores/graphData.js'
import { useUIStateStore } from '../stores/uiState.js'

// Props
const props = defineProps({
  data: {
    type: Object,
    default: null
  },
  type: {
    type: String,
    default: 'node', // 'node' | 'edge'
    validator: (value) => ['node', 'edge'].includes(value)
  }
})

// Emits
const emit = defineEmits(['close'])

// Stores
const graphData = useGraphDataStore()
const uiState = useUIStateStore()

/**
 * 获取类型颜色
 */
function getTypeColor(type) {
  const colors = {
    '编程概念': '#FF6B6B',
    '关键字': '#4ECDC4',
    '语法结构': '#45B7D1',
    '函数': '#96CEB4',
    '方法': '#FFEAA7',
    '数据结构': '#DDA0DD',
    '变量': '#F8C471',
    '表达式': '#85C1E9'
  }
  return colors[type] || '#666666'
}

/**
 * 获取度数颜色
 */
function getDegreeColor(degree) {
  if (degree > 20) return '#FF6B6B'
  if (degree > 10) return '#4ECDC4'
  if (degree > 5) return '#45B7D1'
  if (degree > 2) return '#96CEB4'
  if (degree > 0) return '#FFEAA7'
  return '#DDA0DD'
}

/**
 * 获取入度
 */
function getInDegree(nodeId) {
  return graphData.filteredEdges.filter(edge => edge.target === nodeId).length
}

/**
 * 获取出度
 */
function getOutDegree(nodeId) {
  return graphData.filteredEdges.filter(edge => edge.source === nodeId).length
}

/**
 * 选择节点
 */
function selectNode(nodeId) {
  const node = graphData.getNodeById(nodeId)
  if (node) {
    uiState.selectNode(node)
  }
}
</script>

<style scoped>
.detail-panel {
  position: fixed;
  top: 16px;
  right: 16px;
  width: 400px;
  max-height: 400px;
  background: rgba(0, 0, 0, 0.95);
  border: 1px solid rgba(78, 205, 196, 0.3);
  border-radius: 12px;
  backdrop-filter: blur(15px);
  z-index: 300;
  overflow: hidden;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid rgba(78, 205, 196, 0.2);
  background: rgba(78, 205, 196, 0.1);
}

.panel-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #4ecdc4;
}

.close-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  font-size: 16px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  color: #ff6b6b;
  background: rgba(255, 107, 107, 0.1);
}

.panel-content {
  padding: 16px;
  max-height: 340px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 16px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
  gap: 12px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 600;
  min-width: 60px;
  flex-shrink: 0;
}

.detail-value {
  font-size: 12px;
  color: #ffffff;
  text-align: right;
  word-break: break-word;
  flex: 1;
}

.type-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  color: #000000;
}

.degree-value {
  font-weight: 700;
  font-family: 'Courier New', monospace;
}

.weight-value {
  font-family: 'Courier New', monospace;
  color: #4ecdc4;
}

.node-link {
  color: #4ecdc4 !important;
  cursor: pointer;
  text-decoration: underline;
  transition: color 0.2s ease;
}

.node-link:hover {
  color: #45b7d1 !important;
}

.description-content {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
  padding: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border-left: 3px solid #4ecdc4;
  margin-top: 8px;
}

.connections-info {
  display: flex;
  gap: 16px;
  margin-top: 8px;
}

.connection-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  flex: 1;
}

.connection-label {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 4px;
}

.connection-value {
  font-size: 14px;
  color: #4ecdc4;
  font-weight: 700;
  font-family: 'Courier New', monospace;
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb {
  background: rgba(78, 205, 196, 0.5);
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: rgba(78, 205, 196, 0.7);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-panel {
    width: calc(100vw - 32px);
    max-width: 400px;
    right: 16px;
  }
}

@media (max-width: 480px) {
  .detail-panel {
    width: calc(100vw - 16px);
    right: 8px;
    top: 8px;
  }
  
  .panel-header {
    padding: 12px;
  }
  
  .panel-content {
    padding: 12px;
  }
  
  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .detail-value {
    text-align: left;
  }
}
</style>
