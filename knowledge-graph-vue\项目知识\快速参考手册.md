# 知识图谱项目 - 快速参考手册

## 🚨 紧急问题解决

### 节点消失问题
**症状**: 拖拽节点到边缘时消失
**解决**: 检查LOD系统 (`applyLOD`函数) 和视口裁剪
**文件**: `GraphCanvas.vue` 第1045行

### 边显示异常
**症状**: 边连接到空白区域
**解决**: 检查边的节点引用是否正确
**文件**: `GraphCanvas.vue` 第1195-1200行

### 性能问题
**症状**: 卡顿、FPS低
**解决**: 检查性能模式和对象池使用
**文件**: `GraphCanvas.vue` 第1028行

## 🔍 关键代码位置

### GraphCanvas.vue
```javascript
// 视口裁剪控制
line 299-304: 节点可见性设置
line 951-959: 视口边界计算
line 964-970: 视口检查函数

// LOD渲染系统
line 1045-1061: LOD函数定义
line 323: LOD函数调用

// 性能模式
line 53: 性能模式变量
line 1028-1035: 自动性能模式切换

// 物理引擎
line 1190-1204: 模拟数据设置
line 263-280: 模拟tick处理

// 对象池
line 976-978: 节点对象池
line 994-996: 边对象池
```

### graphData.js
```javascript
// 数据筛选
line 32-64: 节点筛选逻辑
line 67-70: 边筛选逻辑
line 73-78: 统计信息
```

## 🛠️ 常用调试命令

### 浏览器控制台
```javascript
// 检查图数据
window.graphData || window.graphDataStore

// 检查节点图形
window.nodeGraphics

// 检查边图形  
window.edgeGraphics

// 检查模拟状态
window.simulation.alpha()
window.simulation.nodes().length

// 强制重启物理引擎
window.simulation.alpha(0.3).restart()
```

### 性能监控
```javascript
// 检查FPS
console.log('FPS:', window.fps)

// 检查性能模式
console.log('Performance Mode:', window.isPerformanceMode)

// 内存使用
console.log('Memory:', performance.memory?.usedJSHeapSize)
```

## 🔧 快速修复模板

### 禁用节点消失
```javascript
// 在 onSimulationTick 函数中
graphic.visible = true // 强制显示
```

### 禁用LOD系统
```javascript
function applyLOD(graphic, distance) {
  // 注释掉所有内容，禁用LOD
  return
}
```

### 增加视口范围
```javascript
function updateViewportBounds() {
  const padding = 1000 // 增加这个值
  // ...
}
```

### 强制显示所有边
```javascript
// 在边更新逻辑中
if (true) { // 改为 true 强制显示
  // 边绘制逻辑
}
```

## 📋 检查清单

### 节点问题排查
- [ ] 检查 `graphic.visible` 设置
- [ ] 检查 `isInViewport` 函数
- [ ] 检查 `applyLOD` 函数
- [ ] 检查性能模式状态
- [ ] 检查对象池状态

### 边问题排查
- [ ] 检查边的source/target引用
- [ ] 检查filteredEdges逻辑
- [ ] 检查模拟链接设置
- [ ] 检查边图形创建
- [ ] 检查边容器可见性

### 性能问题排查
- [ ] 检查节点数量限制
- [ ] 检查对象池使用
- [ ] 检查事件监听器清理
- [ ] 检查内存泄漏
- [ ] 检查渲染频率

## 🎯 配置参数

### 性能相关
```javascript
maxNodes: 1000,           // 最大节点数
padding: 500,             // 视口填充
lodDistance: 500,         // LOD距离阈值
fpsThreshold: 30,         // 性能模式FPS阈值
```

### 物理引擎
```javascript
linkDistance: 100,        // 边长度
linkStrength: 0.1,        // 边强度
chargeStrength: -300,     // 斥力强度
centerStrength: 0.1,      // 中心引力
```

### 视觉效果
```javascript
nodeRadius: 8,            // 节点半径
edgeWidth: 2,             // 边宽度
labelOffset: 15,          // 标签偏移
animationDuration: 300,   // 动画时长
```

## 🚀 性能优化快速设置

### 高性能模式
```javascript
// 减少节点数量
maxNodes.value = 500

// 启用性能模式
isPerformanceMode = true

// 增加LOD距离
if (distance > 300) { // 降低阈值
  graphic.alpha = 0.3
}
```

### 高质量模式
```javascript
// 增加节点数量
maxNodes.value = 2000

// 禁用性能模式
isPerformanceMode = false

// 禁用LOD
// applyLOD函数返回空
```

## 📞 联系与支持

### 问题报告
1. 描述具体症状
2. 提供控制台错误信息
3. 说明操作步骤
4. 附上相关代码片段

### 调试信息收集
```javascript
// 收集调试信息
const debugInfo = {
  nodeCount: graphData.filteredNodes.length,
  edgeCount: graphData.filteredEdges.length,
  performanceMode: isPerformanceMode,
  fps: window.fps,
  memory: performance.memory?.usedJSHeapSize,
  viewport: viewportBounds,
  simulation: {
    alpha: simulation.alpha(),
    nodes: simulation.nodes().length,
    links: simulation.force('link').links().length
  }
}
console.log('Debug Info:', debugInfo)
```

## 🔄 版本更新注意事项

### 更新前检查
- [ ] 备份当前配置
- [ ] 记录自定义修改
- [ ] 测试核心功能
- [ ] 检查依赖兼容性

### 更新后验证
- [ ] 节点显示正常
- [ ] 边连接正确
- [ ] 交互功能正常
- [ ] 性能表现良好
- [ ] 无控制台错误

---

**最后更新**: 2025-07-30
**维护者**: Augment Agent
**版本**: 1.0.0
