<template>
  <div class="stats-display">
    <h3 class="section-title">📊 实时统计</h3>
    
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-value">{{ graphData.stats.totalNodes }}</div>
        <div class="stat-label">总节点数</div>
      </div>
      
      <div class="stat-card">
        <div class="stat-value">{{ graphData.stats.totalEdges }}</div>
        <div class="stat-label">总边数</div>
      </div>
      
      <div class="stat-card">
        <div class="stat-value highlight">{{ graphData.stats.visibleNodes }}</div>
        <div class="stat-label">可见节点</div>
      </div>
      
      <div class="stat-card">
        <div class="stat-value highlight">{{ graphData.stats.visibleEdges }}</div>
        <div class="stat-label">可见边数</div>
      </div>
    </div>
    
    <!-- 筛选效率指示器 -->
    <div class="filter-efficiency">
      <div class="efficiency-bar">
        <div 
          class="efficiency-fill" 
          :style="{ width: filterEfficiency + '%' }"
        ></div>
      </div>
      <div class="efficiency-text">
        筛选效率: {{ filterEfficiency.toFixed(1) }}%
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useGraphDataStore } from '../stores/graphData.js'

const graphData = useGraphDataStore()

/**
 * 计算筛选效率（可见节点占总节点的百分比）
 */
const filterEfficiency = computed(() => {
  if (graphData.stats.totalNodes === 0) return 0
  return (graphData.stats.visibleNodes / graphData.stats.totalNodes) * 100
})
</script>

<style scoped>
.stats-display {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(78, 205, 196, 0.2);
  border-radius: 12px;
  padding: 16px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #4ecdc4;
  display: flex;
  align-items: center;
  gap: 8px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 16px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 12px;
  text-align: center;
  transition: all 0.2s ease;
}

.stat-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(78, 205, 196, 0.3);
  transform: translateY(-1px);
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 4px;
  font-family: 'Courier New', monospace;
}

.stat-value.highlight {
  color: #4ecdc4;
}

.stat-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.filter-efficiency {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.efficiency-bar {
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 8px;
}

.efficiency-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff6b6b, #ffeaa7, #4ecdc4);
  border-radius: 3px;
  transition: width 0.5s ease;
  position: relative;
}

.efficiency-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.efficiency-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  font-weight: 500;
}

/* 数字变化动画 */
.stat-value {
  transition: all 0.3s ease;
}

.stat-card:hover .stat-value {
  transform: scale(1.05);
}

/* 响应式调整 */
@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .stat-card {
    padding: 8px;
  }
  
  .stat-value {
    font-size: 18px;
  }
}
</style>
