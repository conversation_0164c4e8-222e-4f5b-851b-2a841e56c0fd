# 🚀 Vue 3 + D3.js + Pixi.js 知识图谱可视化

基于 Vue 3.4、D3.js 和 Pixi.js 构建的高性能知识图谱可视化应用，专为大规模图数据设计。

## ✨ 特性

### 🎨 视觉设计

- **现代化 UI**: 深色主题，渐变背景，科技感十足
- **响应式布局**: 适配不同屏幕尺寸
- **流畅动画**: 60fps 渲染，GPU 加速
- **交互反馈**: 悬停、点击、缩放等丰富交互

### ⚡ 性能优化

- **GPU 渲染**: 使用 Pixi.js WebGL 渲染引擎
- **力导向布局**: D3.js 物理引擎，自然的节点分布
- **大数据支持**: 可处理数千个节点和边
- **实时监控**: FPS、内存使用等性能指标

### 🔧 功能特性

- **智能筛选**: 按度数、类型、搜索关键词筛选
- **节点分级**: 根据连接度数自动分类和着色
- **物理控制**: 可启停物理引擎，调整模拟参数
- **视图操作**: 缩放、平移、重置视图
- **详情面板**: 点击查看节点/边的详细信息

## 🏗️ 技术架构

### 前端框架

- **Vue 3.4**: 组合式 API，响应式数据管理
- **Pinia**: 状态管理，模块化设计
- **Vite**: 快速构建工具

### 图形渲染

- **Pixi.js 7.4**: WebGL 渲染引擎，GPU 加速
- **D3.js 7.9**: 力导向算法，数据驱动
- **Canvas API**: 高性能图形绘制

### 数据处理

- **JSON 格式**: 标准化数据结构
- **实时筛选**: 响应式数据过滤
- **内存优化**: 大数据集处理

## 📁 项目结构

```
knowledge-graph-vue/
├── public/
│   ├── graph_data.json          # 知识图谱数据
│   └── favicon.ico
├── src/
│   ├── components/              # Vue 组件
│   │   ├── KnowledgeGraphContainer.vue  # 主容器
│   │   ├── Sidebar.vue                  # 侧边栏
│   │   ├── GraphCanvas.vue              # 图谱画布
│   │   ├── SimpleGraphCanvas.vue        # 简化版画布
│   │   ├── FilterControls.vue           # 筛选控件
│   │   ├── StatsDisplay.vue             # 统计显示
│   │   ├── PhysicsControls.vue          # 物理引擎控制
│   │   ├── PerformanceSettings.vue      # 性能设置
│   │   ├── DegreeLegend.vue             # 度数图例
│   │   ├── DetailPanel.vue              # 详情面板
│   │   ├── LoadingOverlay.vue           # 加载遮罩
│   │   └── PerformancePanel.vue         # 性能监控
│   ├── stores/                  # Pinia 状态管理
│   │   ├── graphData.js                 # 图数据管理
│   │   └── uiState.js                   # UI 状态管理
│   ├── App.vue                  # 根组件
│   └── main.js                  # 入口文件
├── demo.html                    # 静态演示页面
├── test.html                    # Vue 3 测试页面
├── package.json                 # 项目配置
├── vite.config.js              # Vite 配置
└── README.md                   # 项目文档
```

## 🚀 快速开始

### 环境要求

- Node.js >= 18.0.0
- npm >= 8.0.0

### 安装依赖

```bash
cd knowledge-graph-vue
npm install
```

### 开发模式

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览构建结果

```bash
npm run preview
```

## 📊 数据格式

知识图谱数据采用标准 JSON 格式：

```json
{
  "nodes": [
    {
      "id": "node_1",
      "title": "节点标题",
      "type": "节点类型",
      "degree": 15,
      "frequency": 100,
      "description": "节点描述"
    }
  ],
  "edges": [
    {
      "id": "edge_1",
      "source": "node_1",
      "target": "node_2",
      "weight": 0.8,
      "combined_degree": 25,
      "description": "边描述"
    }
  ]
}
```

## 🎮 使用说明

### 基本操作

- **鼠标滚轮**: 缩放视图
- **鼠标拖拽**: 平移视图
- **点击节点**: 查看详情
- **点击空白**: 清除选择

### 快捷键

- **空格键**: 切换物理引擎
- **R 键**: 重置视图
- **ESC 键**: 清除选择
- **F 键**: 切换全屏

### 筛选功能

- **度数范围**: 按节点连接数筛选
- **节点类型**: 按分类筛选
- **关键词搜索**: 按标题/描述搜索
- **最大节点数**: 限制显示数量

## 🎨 节点分级

根据节点度数自动分级着色：

| 级别     | 度数范围 | 颜色    | 说明         |
| -------- | -------- | ------- | ------------ |
| 超级节点 | >20      | 🔴 红色 | 核心枢纽节点 |
| 重要节点 | 11-20    | 🔵 青色 | 重要连接节点 |
| 活跃节点 | 6-10     | 🟦 蓝色 | 活跃参与节点 |
| 普通节点 | 3-5      | 🟢 绿色 | 一般连接节点 |
| 边缘节点 | 1-2      | 🟡 黄色 | 边缘参与节点 |
| 孤立节点 | 0        | 🟣 紫色 | 独立存在节点 |

## 🔧 配置选项

### 性能设置

- **GPU 加速**: 启用 WebGL 渲染
- **目标 FPS**: 设置渲染帧率
- **最大节点数**: 限制渲染数量
- **FPS 监控**: 显示性能指标

### 物理引擎

- **力导向强度**: 调整节点排斥力
- **连接距离**: 设置边的理想长度
- **碰撞检测**: 防止节点重叠
- **阻尼系数**: 控制运动衰减

## 🌟 演示页面

项目包含多个演示页面：

1. **demo.html**: 纯 JavaScript + D3 + Pixi.js 实现
2. **test.html**: Vue 3 + CDN 版本
3. **主应用**: 完整的 Vue 3 + Vite 版本

可通过 HTTP 服务器访问：

```bash
python -m http.server 8080
# 访问 http://localhost:8080/demo.html
```

## 🔍 故障排除

### 常见问题

1. **Node.js 版本兼容性**

   - 确保使用 Node.js >= 18.0.0
   - 如遇到版本问题，可降级相关依赖

2. **数据加载失败**

   - 检查 `public/graph_data.json` 文件是否存在
   - 确保数据格式正确

3. **渲染性能问题**

   - 启用 GPU 加速
   - 减少最大节点数
   - 关闭不必要的动画效果

4. **开发服务器启动失败**
   - 清除 node_modules 重新安装
   - 检查端口是否被占用
   - 使用静态 HTML 页面作为备选

---

**🚀 开始探索知识图谱的无限可能！**

### Lint with [ESLint](https://eslint.org/)

```sh
npm run lint
```
