<template>
  <div class="loading-overlay">
    <div class="loading-content">
      <div class="loading-spinner"></div>
      <h3>🚀 D3 + Pixi.js 知识图谱</h3>
      <p>{{ message || '正在初始化 GPU 渲染...' }}</p>
      <div class="loading-progress">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: progress + '%' }"></div>
        </div>
        <span class="progress-text">{{ Math.round(progress) }}%</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

// Props
const props = defineProps({
  message: {
    type: String,
    default: '正在初始化 GPU 渲染...'
  }
})

// 模拟加载进度
const progress = ref(0)
let progressInterval = null

/**
 * 启动进度动画
 */
function startProgress() {
  progress.value = 0
  progressInterval = setInterval(() => {
    if (progress.value < 90) {
      // 前90%快速增长
      progress.value += Math.random() * 15
    } else if (progress.value < 95) {
      // 90-95%慢速增长
      progress.value += Math.random() * 2
    } else {
      // 95%以上非常慢
      progress.value += Math.random() * 0.5
    }
    
    if (progress.value >= 100) {
      progress.value = 100
      clearInterval(progressInterval)
    }
  }, 100)
}

/**
 * 停止进度动画
 */
function stopProgress() {
  if (progressInterval) {
    clearInterval(progressInterval)
    progressInterval = null
  }
}

onMounted(() => {
  startProgress()
})

onUnmounted(() => {
  stopProgress()
})
</script>

<style scoped>
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f0f23 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.loading-content {
  text-align: center;
  max-width: 400px;
  padding: 40px;
}

.loading-spinner {
  margin: 0 auto 24px;
  border: 4px solid rgba(78, 205, 196, 0.3);
  border-top: 4px solid #4ecdc4;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-content h3 {
  margin-bottom: 16px;
  color: #4ecdc4;
  font-size: 24px;
  font-weight: 600;
}

.loading-content p {
  margin-bottom: 32px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
  line-height: 1.5;
}

.loading-progress {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4ecdc4, #45b7d1);
  border-radius: 4px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-text {
  color: #4ecdc4;
  font-size: 14px;
  font-weight: 600;
  min-width: 40px;
  text-align: right;
}

/* 星空背景效果 */
.loading-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(2px 2px at 20px 30px, #fff, transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
    radial-gradient(1px 1px at 90px 40px, #fff, transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
    radial-gradient(2px 2px at 160px 30px, #fff, transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: stars 20s linear infinite;
  opacity: 0.3;
}

@keyframes stars {
  0% { transform: translateY(0); }
  100% { transform: translateY(-100px); }
}
</style>
