<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pixi.js 基础测试</title>
    <script src="https://pixijs.download/release/pixi.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #0a0a0a;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .info {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .canvas-container {
            width: 800px;
            height: 600px;
            border: 2px solid #4ecdc4;
            border-radius: 8px;
            background: #1a1a1a;
            position: relative;
        }
        
        .status {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            color: #4ecdc4;
        }
        
        .error {
            color: #ff6b6b;
        }
        
        .success {
            color: #4ecdc4;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 Pixi.js 基础测试</h1>
        
        <div class="info">
            <h3>测试目标</h3>
            <p>验证 Pixi.js 是否能正常初始化并渲染基本图形</p>
            <div id="test-results"></div>
        </div>
        
        <div class="canvas-container" id="canvas-container">
            <div class="status" id="status">初始化中...</div>
        </div>
    </div>

    <script>
        const statusEl = document.getElementById('status');
        const resultsEl = document.getElementById('test-results');
        const containerEl = document.getElementById('canvas-container');
        
        function updateStatus(message, isError = false) {
            statusEl.textContent = message;
            statusEl.className = isError ? 'status error' : 'status success';
            console.log(message);
        }
        
        function addResult(message, isError = false) {
            const div = document.createElement('div');
            div.textContent = message;
            div.style.color = isError ? '#ff6b6b' : '#4ecdc4';
            div.style.marginBottom = '5px';
            resultsEl.appendChild(div);
        }

        async function testPixi() {
            try {
                updateStatus('检查 Pixi.js 可用性...');
                
                // 检查 PIXI 是否可用
                if (typeof PIXI === 'undefined') {
                    throw new Error('PIXI 未定义 - 库可能未正确加载');
                }
                addResult('✅ PIXI 库已加载');
                
                // 检查容器
                if (!containerEl) {
                    throw new Error('Canvas 容器未找到');
                }
                addResult('✅ Canvas 容器已找到');
                
                updateStatus('创建 Pixi.js 应用...');
                
                // 创建应用
                const app = new PIXI.Application({
                    width: 760,
                    height: 560,
                    backgroundColor: 0x0a0a0a,
                    antialias: true
                });
                
                addResult('✅ Pixi.js 应用创建成功');
                
                // 获取画布
                const canvas = app.canvas || app.view;
                if (!canvas) {
                    throw new Error('无法获取画布');
                }
                addResult('✅ 画布获取成功');
                
                updateStatus('添加画布到容器...');
                
                // 添加到容器
                containerEl.appendChild(canvas);
                addResult('✅ 画布已添加到容器');
                
                updateStatus('创建测试图形...');
                
                // 创建一些测试图形
                
                // 1. 背景矩形
                const bg = new PIXI.Graphics();
                bg.beginFill(0x1a1a1a);
                bg.drawRect(0, 0, app.screen.width, app.screen.height);
                bg.endFill();
                app.stage.addChild(bg);
                
                // 2. 中心圆
                const centerCircle = new PIXI.Graphics();
                centerCircle.beginFill(0x4ecdc4);
                centerCircle.drawCircle(app.screen.width / 2, app.screen.height / 2, 50);
                centerCircle.endFill();
                app.stage.addChild(centerCircle);
                
                // 3. 一些随机圆点
                for (let i = 0; i < 20; i++) {
                    const circle = new PIXI.Graphics();
                    const x = Math.random() * app.screen.width;
                    const y = Math.random() * app.screen.height;
                    const radius = Math.random() * 10 + 5;
                    const color = Math.random() * 0xffffff;
                    
                    circle.beginFill(color, 0.7);
                    circle.drawCircle(x, y, radius);
                    circle.endFill();
                    
                    // 添加交互
                    circle.interactive = true;
                    circle.buttonMode = true;
                    circle.on('pointerdown', () => {
                        addResult(`点击了圆点 ${i + 1}`);
                    });
                    
                    app.stage.addChild(circle);
                }
                
                // 4. 连接线
                const line = new PIXI.Graphics();
                line.lineStyle(2, 0x45b7d1, 0.8);
                line.moveTo(100, 100);
                line.lineTo(app.screen.width - 100, 100);
                line.lineTo(app.screen.width - 100, app.screen.height - 100);
                line.lineTo(100, app.screen.height - 100);
                line.lineTo(100, 100);
                app.stage.addChild(line);
                
                addResult('✅ 测试图形创建完成');
                
                // 5. 添加文本
                const text = new PIXI.Text('Pixi.js 测试成功！', {
                    fontFamily: 'Arial',
                    fontSize: 24,
                    fill: 0xffffff,
                    align: 'center'
                });
                text.x = app.screen.width / 2 - text.width / 2;
                text.y = 50;
                app.stage.addChild(text);
                
                addResult('✅ 文本渲染成功');
                
                updateStatus('✅ 所有测试通过！');
                addResult('🎉 Pixi.js 完全正常工作');
                
                // 添加简单动画
                let rotation = 0;
                app.ticker.add(() => {
                    rotation += 0.01;
                    centerCircle.rotation = rotation;
                });
                
                addResult('✅ 动画循环启动');
                
            } catch (error) {
                updateStatus(`❌ 测试失败: ${error.message}`, true);
                addResult(`❌ 错误: ${error.message}`, true);
                console.error('Pixi.js 测试失败:', error);
            }
        }

        // 等待页面加载完成后开始测试
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', testPixi);
        } else {
            testPixi();
        }
    </script>
</body>
</html>
