<template>
  <div class="physics-controls">
    <h3 class="section-title">⚡ 物理引擎控制</h3>
    
    <div class="control-buttons">
      <button 
        class="btn"
        :class="uiState.physicsRunning ? 'btn-danger' : 'btn-primary'"
        @click="togglePhysics"
        :title="uiState.physicsRunning ? '停止物理模拟' : '启动物理模拟'"
      >
        {{ uiState.physicsRunning ? '⏸️ 停止物理' : '▶️ 启动物理' }}
      </button>
      
      <button 
        class="btn btn-secondary"
        @click="restartLayout"
        :disabled="!uiState.physicsRunning"
        title="重新开始布局计算"
      >
        🔄 重新布局
      </button>
      
      <button 
        class="btn btn-warning"
        @click="resetView"
        title="重置视图到中心位置"
      >
        🎯 重置视图
      </button>
    </div>
    
    <!-- 物理引擎状态指示器 -->
    <div class="physics-status">
      <div class="status-indicator">
        <div 
          class="status-dot" 
          :class="{ active: uiState.physicsRunning }"
        ></div>
        <span class="status-text">
          {{ uiState.physicsRunning ? '物理引擎运行中' : '物理引擎已停止' }}
        </span>
      </div>
      
      <!-- Alpha 值显示 -->
      <div class="alpha-display">
        <label class="alpha-label">模拟强度:</label>
        <div class="alpha-bar">
          <div 
            class="alpha-fill" 
            :style="{ width: (uiState.physicsAlpha * 100) + '%' }"
          ></div>
        </div>
        <span class="alpha-value">{{ (uiState.physicsAlpha * 100).toFixed(1) }}%</span>
      </div>
    </div>
    
    <!-- 快捷键提示 */
    <div class="shortcuts">
      <div class="shortcuts-title">快捷键:</div>
      <div class="shortcut-item">
        <kbd>空格</kbd> 切换物理引擎
      </div>
      <div class="shortcut-item">
        <kbd>R</kbd> 重置视图
      </div>
      <div class="shortcut-item">
        <kbd>ESC</kbd> 清除选择
      </div>
    </div>
  </div>
</template>

<script setup>
import { useUIStateStore } from '../stores/uiState.js'

const uiState = useUIStateStore()

/**
 * 切换物理引擎状态
 */
function togglePhysics() {
  uiState.togglePhysics()
  
  // 发送事件给图谱组件
  if (uiState.physicsRunning) {
    // 重启物理模拟
    uiState.setPhysicsAlpha(0.3)
  }
}

/**
 * 重新开始布局
 */
function restartLayout() {
  if (uiState.physicsRunning) {
    // 重置 alpha 值以重新开始模拟
    uiState.setPhysicsAlpha(1.0)
  }
}

/**
 * 重置视图
 */
function resetView() {
  uiState.resetViewTransform()
}
</script>

<style scoped>
.physics-controls {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(78, 205, 196, 0.2);
  border-radius: 12px;
  padding: 16px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #4ecdc4;
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.control-buttons .btn {
  width: 100%;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.control-buttons .btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.physics-status {
  padding: 12px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  margin-bottom: 16px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #666;
  transition: all 0.3s ease;
}

.status-dot.active {
  background: #4ecdc4;
  box-shadow: 0 0 10px rgba(78, 205, 196, 0.5);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.status-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

.alpha-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.alpha-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  min-width: 60px;
}

.alpha-bar {
  flex: 1;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.alpha-fill {
  height: 100%;
  background: linear-gradient(90deg, #4ecdc4, #45b7d1);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.alpha-value {
  font-size: 11px;
  color: #4ecdc4;
  font-weight: 600;
  min-width: 35px;
  text-align: right;
  font-family: 'Courier New', monospace;
}

.shortcuts {
  padding: 12px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.shortcuts-title {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8px;
  font-weight: 600;
}

.shortcut-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
}

.shortcut-item:last-child {
  margin-bottom: 0;
}

kbd {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 10px;
  font-family: 'Courier New', monospace;
  color: #4ecdc4;
  min-width: 24px;
  text-align: center;
}

/* 按钮悬停效果 */
.control-buttons .btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.control-buttons .btn:active:not(:disabled) {
  transform: translateY(0);
}
</style>
