# 知识图谱项目 - 节点消失问题解决方案

## 📋 问题描述

### 现象
- 页面刚加载时，所有节点都正常显示
- 当用鼠标拖拽节点到画面边缘区域时，节点会突然消失
- 将节点拖回中心区域时，节点又会重新显示
- 边上显示"节点未显示"的红色标签

### 用户体验影响
- 严重影响交互体验
- 用户无法自由拖拽和查看边缘节点
- 造成困惑和不信任感

## 🔍 问题根源分析

### 1. 视口裁剪系统 (Viewport Culling)
**位置**: `GraphCanvas.vue` 第299-304行
```javascript
// 视口裁剪
const inViewport = isInViewport(node.x, node.y, 20)
graphic.visible = inViewport
```

**问题**: 
- 原始padding只有100px，过于严格
- 节点稍微移出屏幕就被隐藏

### 2. LOD渲染系统 (Level of Detail) ⭐ **主要原因**
**位置**: `GraphCanvas.vue` 第1045-1059行
```javascript
function applyLOD(graphic, distance) {
  if (isPerformanceMode) {
    if (distance > 500) {
      graphic.visible = false  // 🚨 这里是问题所在
    }
  }
}
```

**问题**:
- 当节点距离屏幕中心超过500px时，直接设置为不可见
- 这是性能优化机制，但过于激进
- 即使禁用了视口裁剪，LOD系统仍会隐藏节点

### 3. 自动性能模式
**位置**: `GraphCanvas.vue` 第1028-1035行
```javascript
if (fps < 30 && !isPerformanceMode) {
  isPerformanceMode = true  // 自动启用性能模式
}
```

**问题**:
- FPS低于30时自动启用性能模式
- 性能模式下LOD系统更加激进

## 🔧 解决方案

### 1. 增加视口边界填充
```javascript
// 从 100 增加到 500
const padding = 500 // 增加边界填充，让节点在更远的地方才被裁剪
```

### 2. 禁用节点的视口裁剪
```javascript
graphic.visible = true // 临时禁用视口裁剪，始终显示节点
```

### 3. 修改LOD系统 ⭐ **关键修改**
```javascript
function applyLOD(graphic, distance) {
  if (isPerformanceMode) {
    if (distance > 500) {
      // graphic.visible = false // 禁用距离隐藏
      graphic.alpha = 0.3 // 改为降低透明度
      graphic.filters = [] // 移除滤镜效果
    }
  }
}
```

### 4. 禁用自动性能模式
```javascript
// isPerformanceMode = true // 临时禁用自动性能模式
console.log('FPS低但未启用性能模式 (已禁用自动切换)')
```

## ⚠️ 重要注意事项

### 1. 多层可见性控制
- 项目中有多个系统控制节点可见性：
  - 视口裁剪 (Viewport Culling)
  - LOD渲染 (Level of Detail)
  - 性能模式 (Performance Mode)
- **必须检查所有层级**，单独修改一个可能无效

### 2. 执行顺序很重要
```javascript
graphic.visible = true      // 1. 设置可见
// ...
applyLOD(graphic, distance) // 2. LOD可能覆盖设置
```
- LOD在视口裁剪之后执行
- 后执行的代码会覆盖前面的设置

### 3. 性能与体验的平衡
- 视口裁剪和LOD是重要的性能优化
- 完全禁用可能影响大图性能
- 建议使用透明度而不是完全隐藏

## 🛠️ 调试技巧

### 1. 搜索所有可见性设置
```bash
# 搜索所有设置visible的地方
grep -n "\.visible\s*=" GraphCanvas.vue
```

### 2. 添加调试日志
```javascript
console.log(`节点 ${nodeId} 状态:`, {
  position: `(${node.x}, ${node.y})`,
  distance: distance,
  inViewport,
  isPerformanceMode,
  visibleBefore: graphic.visible
})
```

### 3. 检查性能模式状态
```javascript
console.log('性能模式状态:', isPerformanceMode)
```

## 🚨 常见错误

### 1. 只修改视口裁剪
- ❌ 只禁用 `isInViewport` 检查
- ✅ 需要同时检查LOD系统

### 2. 忽略性能模式影响
- ❌ 没有考虑自动性能模式的影响
- ✅ 需要监控和控制性能模式

### 3. 不理解执行顺序
- ❌ 认为设置 `visible = true` 就够了
- ✅ 理解后续代码可能覆盖设置

## 📁 相关文件

- `src/components/GraphCanvas.vue` - 主要渲染逻辑
- `src/stores/graphData.js` - 数据筛选逻辑
- `src/stores/uiState.js` - UI状态管理

## 🎯 最佳实践

1. **渐进式调试**: 一步步排查每个可见性控制系统
2. **保留性能优化**: 使用透明度而不是完全隐藏
3. **添加调试信息**: 在关键位置添加日志
4. **文档记录**: 记录每次修改的原因和影响

## 🔄 后续优化建议

1. **可配置的距离阈值**: 允许用户调整LOD距离
2. **更智能的性能模式**: 基于节点数量而不是FPS
3. **渐变透明度**: 距离越远透明度越低
4. **视口边界指示器**: 显示当前视口范围
