<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Vue 3 知识图谱测试</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script src="https://pixijs.download/release/pixi.min.js"></script>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f0f23 100%);
        color: #ffffff;
        overflow: hidden;
        height: 100vh;
      }

      .app {
        display: flex;
        height: 100vh;
      }

      .sidebar {
        width: 350px;
        background: rgba(0, 0, 0, 0.9);
        border-right: 1px solid rgba(78, 205, 196, 0.3);
        backdrop-filter: blur(10px);
        padding: 20px;
        overflow-y: auto;
      }

      .sidebar h1 {
        color: #4ecdc4;
        margin-bottom: 24px;
        font-size: 20px;
        text-align: center;
      }

      .stats-section {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(78, 205, 196, 0.2);
        border-radius: 12px;
        padding: 16px;
        margin-bottom: 20px;
      }

      .stats-section h3 {
        color: #4ecdc4;
        margin-bottom: 12px;
        font-size: 16px;
      }

      .stats-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;
      }

      .stat-card {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 12px;
        text-align: center;
      }

      .stat-value {
        font-size: 20px;
        font-weight: 700;
        color: #4ecdc4;
        margin-bottom: 4px;
        font-family: 'Courier New', monospace;
      }

      .stat-label {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.7);
      }

      .canvas-area {
        flex: 1;
        position: relative;
      }

      .loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        color: #4ecdc4;
      }

      .loading-spinner {
        border: 3px solid rgba(78, 205, 196, 0.3);
        border-top: 3px solid #4ecdc4;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .controls {
        position: absolute;
        bottom: 16px;
        right: 16px;
        display: flex;
        gap: 8px;
      }

      .control-btn {
        width: 40px;
        height: 40px;
        background: rgba(0, 0, 0, 0.8);
        border: 1px solid rgba(78, 205, 196, 0.3);
        border-radius: 50%;
        color: #4ecdc4;
        cursor: pointer;
        font-size: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
      }

      .control-btn:hover {
        background: rgba(78, 205, 196, 0.2);
        transform: scale(1.1);
      }

      .error {
        color: #ff6b6b;
        text-align: center;
        padding: 20px;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div class="app">
        <div class="sidebar">
          <h1>🚀 Vue 3 + D3 + Pixi.js 知识图谱</h1>

          <div class="stats-section">
            <h3>📊 数据统计</h3>
            <div class="stats-grid">
              <div class="stat-card">
                <div class="stat-value">{{ nodeCount }}</div>
                <div class="stat-label">节点数</div>
              </div>
              <div class="stat-card">
                <div class="stat-value">{{ edgeCount }}</div>
                <div class="stat-label">边数</div>
              </div>
            </div>
          </div>

          <div class="stats-section">
            <h3>⚡ 状态</h3>
            <div v-if="loading" class="error">正在加载数据...</div>
            <div v-else-if="error" class="error">{{ error }}</div>
            <div v-else style="color: #4ecdc4">✅ 数据加载成功</div>
          </div>
        </div>

        <div class="canvas-area">
          <div v-if="loading" class="loading">
            <div class="loading-spinner"></div>
            <h3>正在初始化图谱...</h3>
          </div>

          <div v-else-if="error" class="loading">
            <h3>❌ 加载失败</h3>
            <p>{{ error }}</p>
          </div>

          <div v-else ref="canvasContainer" style="width: 100%; height: 100%"></div>

          <div v-if="!loading && !error" class="controls">
            <button
              class="control-btn"
              @click="togglePhysics"
              :title="physicsRunning ? '停止物理引擎' : '启动物理引擎'"
            >
              {{ physicsRunning ? '⏸️' : '▶️' }}
            </button>
            <button class="control-btn" @click="resetView" title="重置视图">🎯</button>
          </div>
        </div>
      </div>
    </div>

    <script>
      const { createApp, ref, onMounted, computed } = Vue

      createApp({
        setup() {
          const loading = ref(true)
          const error = ref(null)
          const graphData = ref(null)
          const canvasContainer = ref(null)
          const physicsRunning = ref(true)

          let app = null
          let simulation = null

          const nodeCount = computed(() => graphData.value?.nodes?.length || 0)
          const edgeCount = computed(() => graphData.value?.edges?.length || 0)

          async function loadData() {
            try {
              const response = await fetch('./public/graph_data.json')
              if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`)
              }
              const data = await response.json()
              graphData.value = data
              console.log('数据加载成功:', { nodes: data.nodes?.length, edges: data.edges?.length })
              return true
            } catch (err) {
              console.error('数据加载失败:', err)
              error.value = err.message
              return false
            }
          }

          async function initGraph() {
            if (!canvasContainer.value || !graphData.value) return

            try {
              // 初始化 Pixi.js
              app = new PIXI.Application({
                width: canvasContainer.value.clientWidth || 800,
                height: canvasContainer.value.clientHeight || 600,
                backgroundColor: 0x0a0a0a,
                antialias: true,
              })

              // 兼容新旧版本的 Pixi.js
              const canvas = app.canvas || app.view
              if (canvas) {
                canvasContainer.value.appendChild(canvas)
              } else {
                throw new Error('无法获取 Pixi.js 画布')
              }

              // 初始化 D3 力导向
              const nodes = graphData.value.nodes || []
              const edges = graphData.value.edges || []

              simulation = d3
                .forceSimulation(nodes)
                .force(
                  'link',
                  d3
                    .forceLink(edges)
                    .id((d) => d.id)
                    .distance(50),
                )
                .force('charge', d3.forceManyBody().strength(-300))
                .force('center', d3.forceCenter(app.screen.width / 2, app.screen.height / 2))
                .force(
                  'collision',
                  d3.forceCollide().radius((d) => Math.sqrt(d.degree || 1) * 2 + 5),
                )
                .on('tick', updatePositions)

              createGraphics(nodes, edges)

              console.log('图谱初始化完成')
            } catch (err) {
              console.error('图谱初始化失败:', err)
              error.value = err.message
            }
          }

          function createGraphics(nodes, edges) {
            // 创建边
            edges.forEach((edge) => {
              const line = new PIXI.Graphics()
              app.stage.addChild(line)
            })

            // 创建节点
            nodes.forEach((node) => {
              const circle = new PIXI.Graphics()
              const radius = Math.sqrt(node.degree || 1) * 2 + 5
              const color = getNodeColor(node.degree || 0)

              circle.beginFill(color, 0.8)
              circle.drawCircle(0, 0, radius)
              circle.endFill()

              circle.interactive = true
              circle.buttonMode = true
              circle.on('pointerdown', () => console.log('节点点击:', node.title || node.id))

              app.stage.addChild(circle)
            })
          }

          function getNodeColor(degree) {
            if (degree > 20) return 0xff6b6b
            if (degree > 10) return 0x4ecdc4
            if (degree > 5) return 0x45b7d1
            if (degree > 2) return 0x96ceb4
            if (degree > 0) return 0xffeaa7
            return 0xdda0dd
          }

          function updatePositions() {
            // 更新图形位置的逻辑
          }

          function togglePhysics() {
            physicsRunning.value = !physicsRunning.value
            if (simulation) {
              if (physicsRunning.value) {
                simulation.alpha(0.3).restart()
              } else {
                simulation.stop()
              }
            }
          }

          function resetView() {
            if (app) {
              app.stage.position.set(0, 0)
              app.stage.scale.set(1)
            }
          }

          onMounted(async () => {
            const success = await loadData()
            if (success) {
              await Vue.nextTick()
              await initGraph()
            }
            loading.value = false
          })

          return {
            loading,
            error,
            nodeCount,
            edgeCount,
            canvasContainer,
            physicsRunning,
            togglePhysics,
            resetView,
          }
        },
      }).mount('#app')
    </script>
  </body>
</html>
