{"name": "knowledge-graph-vue", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": ">=18.0.0"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"d3": "^7.9.0", "lodash-es": "^4.17.21", "pinia": "^3.0.3", "pixi.js": "^7.4.3", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@eslint/js": "^8.57.0", "@vitejs/plugin-vue": "^5.0.0", "@vue/eslint-config-prettier": "^9.0.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.17.0", "globals": "^15.0.0", "prettier": "3.3.0", "vite": "^5.4.0", "vite-plugin-vue-devtools": "^7.0.0"}}