<template>
  <div class="degree-legend">
    <h3 class="section-title">⭐ 节点度数图例</h3>
    
    <div class="legend-list">
      <div 
        v-for="(level, key) in graphData.degreeStats" 
        :key="key"
        class="legend-item"
        :class="{ 'has-nodes': level.count > 0 }"
      >
        <div class="legend-indicator">
          <div 
            class="legend-dot" 
            :style="{ 
              backgroundColor: level.color,
              boxShadow: level.count > 0 ? `0 0 8px ${level.color}40` : 'none'
            }"
          >
            <!-- 超级节点显示星形 -->
            <span v-if="key === 'superNodes' && level.count > 0" class="star-icon">⭐</span>
            <!-- 重要节点显示白色边框 -->
            <div v-else-if="key === 'importantNodes' && level.count > 0" class="important-border"></div>
          </div>
        </div>
        
        <div class="legend-content">
          <div class="legend-label">
            {{ getLevel<PERSON>abel(key) }}
            <span class="degree-range">({{ getDegreeRange(level) }})</span>
          </div>
          <div class="legend-count">
            {{ level.count }} 个节点
          </div>
        </div>
        
        <div class="legend-percentage">
          {{ getPercentage(level.count) }}%
        </div>
      </div>
    </div>
    
    <!-- 图例说明 */
    <div class="legend-description">
      <div class="description-title">说明:</div>
      <div class="description-item">
        <span class="description-icon">⭐</span>
        <span>超级节点 - 核心枢纽</span>
      </div>
      <div class="description-item">
        <span class="description-icon">⚪</span>
        <span>重要节点 - 白色边框</span>
      </div>
      <div class="description-item">
        <span class="description-icon">🔗</span>
        <span>度数 = 连接的边数量</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useGraphDataStore } from '../stores/graphData.js'

const graphData = useGraphDataStore()

/**
 * 获取级别标签
 */
function getLevelLabel(key) {
  const labels = {
    superNodes: '超级节点',
    importantNodes: '重要节点',
    activeNodes: '活跃节点',
    normalNodes: '普通节点',
    edgeNodes: '边缘节点',
    isolatedNodes: '孤立节点'
  }
  return labels[key] || key
}

/**
 * 获取度数范围文本
 */
function getDegreeRange(level) {
  if (level.max === Infinity) {
    return `>${level.min - 1}`
  } else if (level.min === level.max) {
    return `${level.min}`
  } else {
    return `${level.min}-${level.max}`
  }
}

/**
 * 计算百分比
 */
function getPercentage(count) {
  const total = graphData.stats.visibleNodes
  if (total === 0) return 0
  return ((count / total) * 100).toFixed(1)
}

/**
 * 总节点数（用于计算百分比）
 */
const totalVisibleNodes = computed(() => {
  return graphData.stats.visibleNodes
})
</script>

<style scoped>
.degree-legend {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(78, 205, 196, 0.2);
  border-radius: 12px;
  padding: 16px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #4ecdc4;
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  opacity: 0.6;
}

.legend-item.has-nodes {
  opacity: 1;
  background: rgba(255, 255, 255, 0.03);
}

.legend-item:hover.has-nodes {
  background: rgba(255, 255, 255, 0.05);
  transform: translateX(2px);
}

.legend-indicator {
  position: relative;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.legend-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  position: relative;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.legend-item.has-nodes .legend-dot {
  transform: scale(1.1);
}

.star-icon {
  font-size: 8px;
  color: #ffffff;
  text-shadow: 0 0 4px rgba(255, 255, 255, 0.8);
}

.important-border {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 1px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  box-shadow: 0 0 6px rgba(255, 255, 255, 0.3);
}

.legend-content {
  flex: 1;
  min-width: 0;
}

.legend-label {
  font-size: 13px;
  color: #ffffff;
  font-weight: 500;
  margin-bottom: 2px;
}

.degree-range {
  color: rgba(255, 255, 255, 0.6);
  font-size: 11px;
  font-weight: 400;
}

.legend-count {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
}

.legend-percentage {
  font-size: 11px;
  color: #4ecdc4;
  font-weight: 600;
  font-family: 'Courier New', monospace;
  min-width: 35px;
  text-align: right;
}

.legend-description {
  padding: 12px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.description-title {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8px;
  font-weight: 600;
}

.description-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
}

.description-item:last-child {
  margin-bottom: 0;
}

.description-icon {
  font-size: 12px;
  width: 16px;
  text-align: center;
}

/* 动画效果 */
.legend-item.has-nodes .legend-dot {
  animation: gentle-pulse 3s ease-in-out infinite;
}

@keyframes gentle-pulse {
  0%, 100% { 
    transform: scale(1.1); 
    opacity: 1; 
  }
  50% { 
    transform: scale(1.2); 
    opacity: 0.8; 
  }
}

/* 无节点时的样式 */
.legend-item:not(.has-nodes) {
  filter: grayscale(0.8);
}

.legend-item:not(.has-nodes) .legend-dot {
  opacity: 0.3;
}

.legend-item:not(.has-nodes) .legend-count {
  color: rgba(255, 255, 255, 0.4);
}

.legend-item:not(.has-nodes) .legend-percentage {
  color: rgba(255, 255, 255, 0.4);
}
</style>
