{"nodes": [{"id": "函数", "title": "函数", "description": "函数是组织良好、可重复使用的代码块，用于实现特定或相关联的功能。它们通常具有名称，并通过调用来执行预先定义的任务。使用函数可以有效避免重复编写相同的代码，从而提高程序的可维护性和可读性。", "degree": 30, "type": "编程概念", "frequency": 7}, {"id": "调用", "title": "调用", "description": "“调用”是指在Python编程中执行一个函数或方法的过程，通常通过函数名和参数来实现。调用的本质是使Python解释器运行函数体中的代码，从而完成特定的计算或操作。", "degree": 1, "type": "编程概念", "frequency": 2}, {"id": "模块", "title": "模块", "description": "模块是Python中用于组织和复用代码的基本结构，通常是扩展名为.py的独立文件，包含Python定义和语句，如函数、类和变量。模块通过import语句导入使用，有助于组织程序结构、提高代码的可维护性，并支持代码的复用。", "degree": 5, "type": "编程概念", "frequency": 6}, {"id": "DEF", "title": "DEF", "description": "“def”是 Python 编程语言中的一个关键字，用于定义函数。它标志着一个函数声明的开始，是编写函数时不可或缺的语法元素。在 Python 中，开发者通过使用“def”关键字来创建可重用的功能模块，从而提高代码的组织性与可维护性。", "degree": 5, "type": "关键字", "frequency": 5}, {"id": "函数定义", "title": "函数定义", "description": "函数定义是使用def关键字创建函数的语法结构，包含函数名、参数列表和函数体", "degree": 2, "type": "语法结构", "frequency": 1}, {"id": "函数体", "title": "函数体", "description": "函数体是函数定义中缩进的代码块，包含函数执行的具体操作", "degree": 2, "type": "语法结构", "frequency": 1}, {"id": "文档字符串", "title": "文档字符串", "description": "文档字符串是紧跟在函数定义后、位于函数体第一行的字符串，用于描述函数的用途或功能。它通常使用三个双引号括起来（例如\"\"\"文档内容\"\"\"），在Python中被称为docstring，是编写清晰、可维护代码的重要组成部分。", "degree": 3, "type": "语法结构", "frequency": 3}, {"id": "GREET_USER()", "title": "GREET_USER()", "description": "greet_user() 是一个用户自定义的 Python 函数，主要用于接收用户名并打印个性化的问候语。该函数的基本功能是显示问候信息，例如“Hello!”，并可根据传入的用户名参数生成更具针对性的问候内容。它是中文 Python 编程教程中常见的入门示例之一，用于帮助初学者理解函数定义、参数传递和字符串输出的基本概念。", "degree": 8, "type": "函数", "frequency": 3}, {"id": "PRINT()", "title": "PRINT()", "description": "print() 是 Python 的内置函数，用于将指定内容输出到控制台。它是初学者学习 Python 编程时最常接触的函数之一，常用于调试程序、显示变量值或输出程序运行结果。", "degree": 8, "type": "函数", "frequency": 7}, {"id": "函数调用", "title": "函数调用", "description": "函数调用是执行函数定义中代码的过程，通常通过函数名和括号实现", "degree": 11, "type": "编程概念", "frequency": 1}, {"id": "USERNAME", "title": "USERNAME", "description": "username 是函数 greet_user() 的参数，用于接收调用者传入的名字", "degree": 4, "type": "表达式", "frequency": 1}, {"id": "TITLE()", "title": "TITLE()", "description": "title() 是 Python 中字符串对象的一个方法，用于将字符串中每个单词的首字母转换为大写，其余字母转换为小写。该方法常用于格式化文本，使其符合标题样式。", "degree": 6, "type": "方法", "frequency": 9}, {"id": "实参", "title": "实参", "description": "实参（argument）是在调用函数时传递给函数的实际值，用于为函数的形参提供输入数据，从而使函数能够完成其任务。它通常以名值对的形式出现，承担着向函数传递必要信息的作用，是函数执行过程中不可或缺的一部分。", "degree": 10, "type": "编程概念", "frequency": 8}, {"id": "形参", "title": "形参", "description": "形参（parameter）是在函数定义中声明的变量，用于接收调用时传入的实参。它是函数定义中用于接收调用时传入的值或数据的变量名，起到在函数内部使用外部传入信息的桥梁作用。", "degree": 9, "type": "编程概念", "frequency": 8}, {"id": "DISPLAY_MESSAGE()", "title": "DISPLAY_MESSAGE()", "description": "display_message() 是一个用户定义的函数，用于打印指出当前章节或本章主题的消息，通常用于帮助学习者明确当前学习内容的主题。", "degree": 0, "type": "函数", "frequency": 2}, {"id": "FAVORITE_BOOK()", "title": "FAVORITE_BOOK()", "description": "favorite_book() 是一个用户定义的函数，包含一个名为 title 的形参，用于打印包含书名的消息", "degree": 1, "type": "函数", "frequency": 1}, {"id": "TITLE", "title": "TITLE", "description": "title 是 favorite_book() 函数的形参，用于接收书名作为输入", "degree": 1, "type": "表达式", "frequency": 1}, {"id": "位置实参", "title": "位置实参", "description": "位置实参是指在函数调用中根据实参在调用中出现的顺序，将其依次传递给函数定义中按顺序排列的形参的参数传递方式。Python根据这些实参的位置，将它们赋值给对应的形参，因此位置实参必须与函数定义中的形参顺序一致。这种方式要求调用时的参数顺序严格匹配函数定义中的参数顺序。", "degree": 8, "type": "编程概念", "frequency": 10}, {"id": "关键字实参", "title": "关键字实参", "description": "关键字实参是指在函数调用中通过显式指定参数名称（以名称=值的形式）来传递给函数的实参。这种方式使得实参与形参之间的对应关系更加清晰，允许实参的顺序与函数定义中的形参顺序不同，从而增强了函数调用的灵活性和可读性。关键字实参常用于提高代码的可维护性，特别是在函数参数较多或具有默认值的情况下。", "degree": 9, "type": "编程概念", "frequency": 10}, {"id": "LIST", "title": "LIST", "description": "列表是Python中的内建数据结构，用于存储有序的元素集合", "degree": 1, "type": "数据结构", "frequency": 1}, {"id": "DICT", "title": "DICT", "description": "字典是Python中的内建数据结构，用于存储键值对形式的数据", "degree": 1, "type": "数据结构", "frequency": 1}, {"id": "DESCRIBE_PET()", "title": "DESCRIBE_PET()", "description": "describe_pet() 是一个用户定义的 Python 函数，用于显示宠物的种类和名字。该函数接受两个形参：pet_name 和 animal_type，其中 animal_type 具有默认值 'dog'。这使得在调用函数时可以只提供宠物名字，默认将其类型视为狗。该函数常用于教学示例中，帮助初学者理解函数定义、参数传递以及默认参数的用法。", "degree": 6, "type": "函数", "frequency": 5}, {"id": "ANIMAL_TYPE", "title": "ANIMAL_TYPE", "description": "在 describe_pet() 函数中，animal_type 是第二个形参，用于接收并表示宠物的种类，其默认值为 'dog'。该参数用于指定所描述宠物的类型，是函数功能实现的重要组成部分。", "degree": 5, "type": "表达式", "frequency": 3}, {"id": "PET_NAME", "title": "PET_NAME", "description": "在中文Python编程教程中，pet_name 是 describe_pet() 函数的第一个形参，用于接收并表示宠物的名字。作为该函数的必需参数，调用 describe_pet() 时必须为 pet_name 提供实参。", "degree": 6, "type": "表达式", "frequency": 3}, {"id": "DESCRIBE_PET", "title": "DESCRIBE_PET", "description": "describe_pet() 是一个用户定义的函数，用于显示宠物的类型和名字信息", "degree": 5, "type": "函数", "frequency": 1}, {"id": "PRINT", "title": "PRINT", "description": "PRINT 是 Python 的内置函数 print()，用于将信息输出到控制台。该函数在 Python 编程中广泛用于调试、展示程序运行结果以及与用户交互，是初学者学习 Python 时最早接触的函数之一。", "degree": 3, "type": "函数", "frequency": 3}, {"id": "调用函数", "title": "调用函数", "description": "调用函数是通过函数名和括号中的实参来执行函数定义的语法结构", "degree": 2, "type": "语法结构", "frequency": 1}, {"id": "默认值", "title": "默认值", "description": "“默认值”是函数定义中为某些形参预先设定的值，当调用函数时如果未提供对应的参数，则会自动使用该默认值。它用于在函数形参中指定预设值，从而使函数在参数未被显式传入时仍能正常运行。", "degree": 4, "type": "编程概念", "frequency": 4}, {"id": "F<STRING>", "title": "F<STRING>", "description": "f-string 是 Python 中用于格式化字符串的表达式，允许在字符串中嵌入变量值", "degree": 2, "type": "表达式", "frequency": 1}, {"id": "TYPEERROR", "title": "TYPEERROR", "description": "TypeError 是 Python 中的一种异常类型，表示操作或函数应用于错误类型的对象", "degree": 2, "type": "异常类型", "frequency": 1}, {"id": "TRACEBACK", "title": "TRACEBACK", "description": "traceback 是 Python 报错信息的一部分，用于显示错误发生的位置和调用栈信息", "degree": 4, "type": "语法结构", "frequency": 1}, {"id": "MAKE_SHIRT()", "title": "MAKE_SHIRT()", "description": "make_shirt() 是一个用户定义的函数，用于打印T恤的尺码和印刷字样", "degree": 2, "type": "函数", "frequency": 1}, {"id": "DESCRIBE_CITY()", "title": "DESCRIBE_CITY()", "description": "describe_city() 是一个接受城市名称和所属国家作为参数的函数，用于打印该城市所属国家的信息", "degree": 0, "type": "函数", "frequency": 1}, {"id": "RETURN", "title": "RETURN", "description": "“RETURN”是 Python 中的一个关键字，用于从函数中返回值。它在函数执行过程中用于将结果传递给调用者，是构建函数逻辑和实现数据流动的核心语法元素之一。", "degree": 3, "type": "关键字", "frequency": 3}, {"id": "返回值", "title": "返回值", "description": "“返回值”是指函数在执行完毕后返回给调用者的结果，它可以是函数处理数据后的输出，形式上既可以是单个值，也可以是多个值。", "degree": 2, "type": "编程概念", "frequency": 2}, {"id": "GET_FORMATTED_NAME()", "title": "GET_FORMATTED_NAME()", "description": "get_formatted_name() 是一个用户自定义的函数，用于格式化姓名字符串。它接受名和姓作为基本参数，并支持可选的中间名，通过将这些部分组合，返回一个标准格式的全名字符串。该函数常用于需要统一姓名显示格式的场景中。", "degree": 12, "type": "函数", "frequency": 5}, {"id": "FULL_NAME", "title": "FULL_NAME", "description": "full_name 是在 get_formatted_name() 函数中用于存储格式化后的完整姓名字符串的变量。该字符串通常由名、中间名和姓组合而成，用于统一表示用户的完整姓名信息。", "degree": 3, "type": "变量", "frequency": 2}, {"id": "MUSICIAN", "title": "MUSICIAN", "description": "musician 是一个变量，用于存储调用 get_formatted_name() 函数后返回的格式化姓名字符串。该变量接收函数 get_formatted_name() 的返回值，用于保存格式化后的姓名信息。", "degree": 5, "type": "变量", "frequency": 3}, {"id": "F-STRING", "title": "F-STRING", "description": "F-STRING 是 Python 中用于格式化字符串的一种表达式形式，它允许在字符串中直接嵌入变量值，从而实现更简洁和可读的字符串拼接与输出。", "degree": 2, "type": "表达式", "frequency": 2}, {"id": "DEFAULT VALUE", "title": "DEFAULT VALUE", "description": "默认值是函数定义中为某些参数提供的预设值，当调用函数时未提供该参数时将使用默认值", "degree": 1, "type": "编程概念", "frequency": 1}, {"id": "IF", "title": "IF", "description": "“if”是 Python 编程语言中的一种条件语法结构，用于根据条件表达式的布尔值（即 True 或 False）来决定是否执行特定的代码块。它允许程序根据不同的布尔表达式结果执行不同的操作，是实现程序流程控制的基本工具之一。", "degree": 6, "type": "语法结构", "frequency": 5}, {"id": "ELSE", "title": "ELSE", "description": "else语法结构与if配合使用，在if条件不满足时执行else代码块", "degree": 1, "type": "语法结构", "frequency": 1}, {"id": "MIDDLE_NAME", "title": "MIDDLE_NAME", "description": "middle_name是一个变量，用于存储用户是否提供的中间名", "degree": 1, "type": "变量", "frequency": 1}, {"id": "变量", "title": "变量", "description": "变量是用于存储数据值的命名内存位置", "degree": 0, "type": "编程概念", "frequency": 1}, {"id": "打印", "title": "打印", "description": "打印语句用于将变量或表达式的值输出到控制台", "degree": 1, "type": "语句", "frequency": 1}, {"id": "BUILD_PERSON", "title": "BUILD_PERSON", "description": "build_person是一个函数，接受姓名组成部分并返回一个包含这些信息的字典", "degree": 4, "type": "函数", "frequency": 1}, {"id": "字典", "title": "字典", "description": "字典是 Python 中的内建数据结构，用于以键值对的形式存储和快速访问相关联的数据项。它是一种无序且可变的数据结构，广泛用于组织和管理具有映射关系的数据。", "degree": 10, "type": "数据结构", "frequency": 9}, {"id": "列表", "title": "列表", "description": "列表是Python中的一种有序可变的数据结构，用于存储多个元素或项目。它允许在保持元素顺序的同时，对其中的内容进行修改，因此在处理多个相关数据项时非常常用。", "degree": 9, "type": "数据结构", "frequency": 5}, {"id": "数据结构", "title": "数据结构", "description": "", "degree": 2, "type": "", "frequency": 1}, {"id": "BUILD_PERSON()", "title": "BUILD_PERSON()", "description": "build_person() 是一个用户定义的函数，接受名和姓作为参数，并将这些信息存储在一个字典中返回；它还可以接受可选参数如年龄", "degree": 6, "type": "函数", "frequency": 1}, {"id": "FIRST_NAME", "title": "FIRST_NAME", "description": "first_name 是传递给函数 build_person() 的参数，表示人的名字", "degree": 1, "type": "表达式", "frequency": 1}, {"id": "LAST_NAME", "title": "LAST_NAME", "description": "last_name 是传递给函数 build_person() 的参数，表示人的姓氏", "degree": 1, "type": "表达式", "frequency": 1}, {"id": "AGE", "title": "AGE", "description": "age 是 build_person() 函数的一个可选参数，用于表示人的年龄", "degree": 4, "type": "表达式", "frequency": 1}, {"id": "NONE", "title": "NONE", "description": "None 是 Python 中的一个特殊常量和关键字，用于表示空值或无值的状态。它常被用作变量的占位符，或作为函数默认参数的默认值，以指示未提供具体值的情况。", "degree": 2, "type": "关键字", "frequency": 2}, {"id": "FALSE", "title": "FALSE", "description": "False 是 Python 中的布尔值之一，表示逻辑上的假", "degree": 1, "type": "关键字", "frequency": 1}, {"id": "WHILE", "title": "WHILE", "description": "WHILE 是 Python 中的一种循环语法结构，用于在条件表达式为真时重复执行一组语句或代码块。它通过在每次迭代前判断条件是否成立，从而实现代码的反复执行，直到条件不再满足为止。", "degree": 6, "type": "语法结构", "frequency": 6}, {"id": "BREAK", "title": "BREAK", "description": "BREAK 是 Python 中的控制流关键字，用于立即终止当前循环结构，无论是 for 循环还是 while 循环。一旦执行到 break 语句，程序将跳出当前循环体，继续执行循环之后的代码。这使得 break 成为控制循环执行流程的重要工具，常用于在满足特定条件时提前结束循环。", "degree": 1, "type": "关键字", "frequency": 2}, {"id": "INPUT()", "title": "INPUT()", "description": "input() 是 Python 的内置函数，用于从用户处获取输入数据。该函数在程序运行时暂停执行，等待用户通过键盘输入内容，并将输入作为字符串返回，常用于交互式程序中收集用户信息。", "degree": 2, "type": "函数", "frequency": 2}, {"id": "F_STRING", "title": "F_STRING", "description": "f-string 是 Python 3.6+ 引入的字符串格式化语法，用于在字符串中嵌入表达式", "degree": 1, "type": "语法结构", "frequency": 1}, {"id": "F字符串", "title": "F字符串", "description": "F字符串是 Python 中用于格式化字符串的表达式形式，通过在字符串前添加 f 前缀，并在花括号中嵌入变量或表达式，从而实现将变量值直接插入到字符串中的功能。这种方式简洁直观，是 Python 3.6 及以上版本中推荐的字符串格式化方法之一。", "degree": 4, "type": "表达式", "frequency": 2}, {"id": "CITY_COUNTRY()", "title": "CITY_COUNTRY()", "description": "city_country() 是一个用户定义函数，接受城市和国家名称并返回格式化字符串", "degree": 1, "type": "函数", "frequency": 1}, {"id": "MAKE_ALBUM()", "title": "MAKE_ALBUM()", "description": "make_album() 是一个用户定义的函数，用于创建一个包含歌手名和专辑名的字典。该函数还支持可选参数，用于包含歌曲数量，从而使返回的字典更具信息性和灵活性。", "degree": 2, "type": "函数", "frequency": 2}, {"id": "GREET_USERS()", "title": "GREET_USERS()", "description": "GREET_USERS() 是一个用户自定义函数，接受一个名字列表，并为列表中的每个名字打印个性化的问候语。该函数通过遍历名字列表，向每位用户输出定制的问候信息，用于展示如何在 Python 中处理列表和函数定义。", "degree": 4, "type": "函数", "frequency": 2}, {"id": "NAMES", "title": "NAMES", "description": "names 是一个字符串列表，表示用户的名字", "degree": 3, "type": "数据类型", "frequency": 1}, {"id": "FOR", "title": "FOR", "description": "FOR 是 Python 编程语言中的一种循环语法结构，主要用于遍历可迭代对象中的元素。例如，在处理配料列表时，for 循环可以逐项访问列表中的每个配料，实现对数据的逐个操作。这种结构在中文 Python 编程教程社区中被广泛用于教学示例，帮助学习者理解如何高效地处理序列数据。", "degree": 7, "type": "语法结构", "frequency": 5}, {"id": "UNPRINTED_DESIGNS", "title": "UNPRINTED_DESIGNS", "description": "UNPRINTED_DESIGNS 是一个列表，用于存储尚未打印的3D模型设计名称。该列表包含诸如 \"phone case\"、\"robot pendant\" 和 \"dodecahedron\" 等尚未打印的设计项目，用于追踪和管理待打印的设计任务。", "degree": 4, "type": "数据结构", "frequency": 3}, {"id": "COMPLETED_MODELS", "title": "COMPLETED_MODELS", "description": "completed_models 是一个用于存储已打印完成模型名称的列表，通常包含3D模型设计的名称。在初始化时，completed_models 是一个空列表，随着模型打印完成后，其名称会被添加到该列表中。该列表在3D打印相关的Python编程项目中常用于追踪和管理已完成的模型记录。", "degree": 6, "type": "数据结构", "frequency": 3}, {"id": "POP()", "title": "POP()", "description": "POP() 是列表对象的方法，用于移除并返回列表中的元素。默认情况下，POP() 会移除并返回列表中的最后一个元素；但也可以通过传入索引参数，移除并返回指定位置的元素。该方法是 Python 中常用的列表操作之一，常用于数据结构操作和栈的实现。", "degree": 2, "type": "方法", "frequency": 2}, {"id": "APPEND()", "title": "APPEND()", "description": "APPEND() 是 Python 中列表对象的方法，用于将元素添加到列表的末尾。该方法通过调用 append() 函数，将指定元素追加到已有列表的最后一个位置，是操作列表时常用的基本方法之一。", "degree": 1, "type": "方法", "frequency": 2}, {"id": "CURRENT_DESIGN", "title": "CURRENT_DESIGN", "description": "current_design是一个变量，用于存储当前从未打印设计列表中弹出的设计项", "degree": 2, "type": "变量", "frequency": 1}, {"id": "PRINT_MODELS()", "title": "PRINT_MODELS()", "description": "print_models() 是一个用户定义的函数，用于模拟打印设计模型的过程。它接受两个列表作为参数：一个包含未打印设计的列表，另一个用于存储已完成打印的模型。该函数通过遍历未打印的设计列表，依次模拟打印每个设计，并将其从未打印列表移动到已完成列表，从而实现设计从准备阶段到完成阶段的转移。该函数常用于演示如何在Python中处理列表之间的数据迁移和函数的参数传递。", "degree": 6, "type": "函数", "frequency": 4}, {"id": "SHOW_COMPLETED_MODELS()", "title": "SHOW_COMPLETED_MODELS()", "description": "SHOW_COMPLETED_MODELS() 是一个用户定义的函数，用于显示所有已经打印完成的模型或设计项。该函数接受一个包含已打印模型的列表作为参数，并逐一打印出每个模型的名称，从而帮助用户查看已完成的打印任务。", "degree": 4, "type": "函数", "frequency": 3}, {"id": "SHOW_COMPLETED_MODELS", "title": "SHOW_COMPLETED_MODELS", "description": "", "degree": 1, "type": "", "frequency": 1}, {"id": "修改", "title": "修改", "description": "修改指对数据结构（如列表）中的内容进行更改的操作", "degree": 1, "type": "编程概念", "frequency": 1}, {"id": "任务分解", "title": "任务分解", "description": "任务分解是一种将复杂任务拆分为多个小任务以提高代码可读性和可维护性的编程理念", "degree": 1, "type": "编程概念", "frequency": 1}, {"id": "切片表示法", "title": "切片表示法", "description": "切片表示法是用于提取序列子集的语法结构，格式为[start:stop:step]", "degree": 1, "type": "语法结构", "frequency": 1}, {"id": "[:]", "title": "[:]", "description": "[:] 是一种切片表达式，用于创建列表的浅拷贝", "degree": 3, "type": "表达式", "frequency": 1}, {"id": "SHOW_MESSAGES()", "title": "SHOW_MESSAGES()", "description": "show_messages() 是一个用户定义的函数，用于接收一个消息列表并打印其中的每条消息", "degree": 1, "type": "函数", "frequency": 1}, {"id": "SEND_MESSAGES()", "title": "SEND_MESSAGES()", "description": "send_messages() 是一个用户定义的函数，用于接收一个消息列表，将每条消息打印出来并移动到另一个列表中", "degree": 2, "type": "函数", "frequency": 1}, {"id": "SENT_MESSAGES", "title": "SENT_MESSAGES", "description": "sent_messages 是一个列表，用于存储已发送的消息", "degree": 1, "type": "数据结构", "frequency": 1}, {"id": "MAKE_PIZZA()", "title": "MAKE_PIZZA()", "description": "make_pizza() 是一个用户定义的函数，用于打印制作披萨的信息。该函数通常包含尺寸和配料两个参数，其中配料参数使用 *toppings 作为形参，能够接收任意数量的配料，并将其打印出来，从而展示披萨的尺寸和所选配料。", "degree": 6, "type": "函数", "frequency": 3}, {"id": "TOPPINGS", "title": "TOPPINGS", "description": "TOPPINGS 是一个元组，由函数定义中的 *toppings 语法创建，用于收集函数调用中传入的所有配料参数。这种用法允许函数接受任意数量的配料参数，并将它们统一存储在 toppings 元组中，便于在函数内部进行处理。", "degree": 2, "type": "数据结构", "frequency": 2}, {"id": "*TOPPINGS", "title": "*TOPPINGS", "description": "*toppings 是一种函数形参语法结构，在 Python 中使用星号 * 表示，用于接收任意数量的位置实参，并将这些实参打包封装为一个元组。这种语法常用于定义可以接受不定数量参数的函数，提升了函数的灵活性和通用性。", "degree": 8, "type": "语法结构", "frequency": 2}, {"id": "MAKE_PIZZA", "title": "MAKE_PIZZA", "description": "make_pizza 是一个用户自定义的示例函数，用于制作披萨。该函数接受一个尺寸参数以及任意数量的配料参数（通过 *toppings 形式参数实现），并打印出制作披萨的相关信息，包括披萨的尺寸和所使用的配料列表。该函数常用于教学示例中，帮助学习者理解如何定义函数、使用可变参数以及处理函数输入。", "degree": 8, "type": "函数", "frequency": 4}, {"id": "元组", "title": "元组", "description": "元组是 Python 中的不可变序列类型，用于存储多个值", "degree": 2, "type": "数据类型", "frequency": 1}, {"id": "SIZE", "title": "SIZE", "description": "size是make_pizza函数的第一个形参，用于表示比萨的尺寸", "degree": 1, "type": "参数", "frequency": 1}, {"id": "*ARGS", "title": "*ARGS", "description": "*args是一种通用的语法结构，用于收集任意数量的位置实参", "degree": 1, "type": "语法结构", "frequency": 1}, {"id": "BUILD_PROFILE()", "title": "BUILD_PROFILE()", "description": "BUILD_PROFILE() 是一个用户自定义函数，用于接收用户的名和姓以及任意数量的关键字实参，并将这些信息整合存储在一个字典中返回。该函数的主要用途是构建包含用户详细信息的字典结构，便于后续的数据处理或展示。", "degree": 7, "type": "函数", "frequency": 2}, {"id": "FIRST", "title": "FIRST", "description": "first 是 build_profile() 函数的一个位置参数，用于接收用户的名字", "degree": 1, "type": "参数", "frequency": 1}, {"id": "LAST", "title": "LAST", "description": "last 是 build_profile() 函数的一个位置参数，用于接收用户的姓氏", "degree": 1, "type": "参数", "frequency": 1}, {"id": "**USER_INFO", "title": "**USER_INFO", "description": "**user_info 是一种语法结构，表示接收任意数量的关键字实参，并将其打包为一个字典", "degree": 2, "type": "语法结构", "frequency": 1}, {"id": "USER_PROFILE", "title": "USER_PROFILE", "description": "user_profile 是一个变量，用于存储 build_profile() 函数返回的包含用户信息的字典", "degree": 2, "type": "变量", "frequency": 1}, {"id": "**KWARGS", "title": "**KWARGS", "description": "**kwargs是Python函数定义中的语法结构，用于收集任意数量的关键字实参为一个字典", "degree": 2, "type": "语法结构", "frequency": 1}, {"id": "MAKE_CAR", "title": "MAKE_CAR", "description": "make_car是一个函数，用于接收制造商、型号以及任意数量的关键字实参，并将这些信息存储在一个字典中", "degree": 2, "type": "函数", "frequency": 1}, {"id": "IMPORT", "title": "IMPORT", "description": "IMPORT 是 Python 中的关键字，用于导入模块或模块中的对象，使得程序可以访问并使用模块中定义的函数、类和变量。这一机制使得开发者能够在当前程序中复用外部模块提供的功能，从而提高代码的组织性与可维护性。", "degree": 8, "type": "关键字", "frequency": 5}, {"id": "PIZZA", "title": "PIZZA", "description": "pizza是一个用户定义的模块，包含make_pizza()函数，用于演示模块的导入和使用", "degree": 4, "type": "模块", "frequency": 1}, {"id": "MAKING_PIZZAS", "title": "MAKING_PIZZAS", "description": "making_pizzas是一个Python模块，用于导入pizza模块并调用其中的make_pizza()函数", "degree": 2, "type": "模块", "frequency": 1}, {"id": "FROM", "title": "FROM", "description": "“from”是 Python 编程语言中的一个关键字，用于从指定的模块中导入特定的对象。它允许程序员选择性地引入模块中的函数、类或变量，从而提高代码的可读性和效率。例如，使用语句 from module import function，可以只导入所需的功能，而无需加载整个模块。这种导入方式在 Python 编程中非常常见，尤其适用于需要精简命名空间或提高执行效率的场景。", "degree": 4, "type": "关键字", "frequency": 3}, {"id": "AS", "title": "AS", "description": "AS 是 Python 中的一个关键字，用于在 import 语句中为导入的模块、函数或对象指定别名。通过使用 as，程序员可以为导入项创建一个更简洁或更具可读性的名称，从而提高代码的可维护性和可读性。", "degree": 4, "type": "关键字", "frequency": 3}, {"id": "MODULE_NAME.FUNCTION_NAME", "title": "MODULE_NAME.FUNCTION_NAME", "description": "module_name.function_name 是调用模块中函数的语法结构，前缀为模块名，后跟函数名", "degree": 1, "type": "语法结构", "frequency": 1}, {"id": "FROM MODULE_NAME IMPORT FUNCTION_NAME", "title": "FROM MODULE_NAME IMPORT FUNCTION_NAME", "description": "from module_name import function_name 是从模块中导入特定函数的语法结构", "degree": 2, "type": "语法结构", "frequency": 1}, {"id": "FROM MODULE_NAME IMPORT FUNCTION_0 , FUNCTION_1 , FUNCTION_2", "title": "FROM MODULE_NAME IMPORT FUNCTION_0 , FUNCTION_1 , FUNCTION_2", "description": "该语法结构用于从模块中一次导入多个函数，函数名之间用逗号分隔", "degree": 1, "type": "语法结构", "frequency": 1}, {"id": "AS 别名", "title": "AS 别名", "description": "使用 as 给函数或模块指定别名的语法结构，便于简化调用或避免命名冲突", "degree": 2, "type": "语法结构", "frequency": 1}, {"id": "MP()", "title": "MP()", "description": "mp() 是 make_pizza() 函数的别名，通过 import 语句中的 as 关键字指定", "degree": 2, "type": "函数", "frequency": 1}, {"id": "*", "title": "*", "description": "* 是 Python 中的星号运算符，在 import 语句中用作通配符，表示导入模块中的所有对象。它常用于 from module import * 的语法形式，作用是将指定模块中的所有公共名称导入当前命名空间。", "degree": 1, "type": "运算符", "frequency": 2}, {"id": "点号", "title": "点号", "description": "点号（dot notation）是Python中用于访问模块、类或对象属性的语法结构", "degree": 2, "type": "语法结构", "frequency": 1}, {"id": "PEP 8", "title": "PEP 8", "description": "PEP 8是Python的官方编码风格指南，旨在提高代码的可读性和一致性", "degree": 1, "type": "编程概念", "frequency": 1}, {"id": "代码行长度限制", "title": "代码行长度限制", "description": "代码行长度限制是PEP 8中建议的规范，推荐每行代码不超过79个字符", "degree": 1, "type": "编程概念", "frequency": 1}, {"id": "任意数量实参", "title": "任意数量实参", "description": "任意数量实参允许函数接受不定数量的参数，通常使用*args或**kwargs", "degree": 1, "type": "编程概念", "frequency": 1}, {"id": "函数编写指南", "title": "函数编写指南", "description": "“函数编写指南”是一个关于如何组织和书写函数的最佳实践集合，旨在提高代码的可读性和可维护性。该指南汇集了编写清晰、结构良好的函数的建议，帮助开发者在编程过程中遵循一致的风格和高质量的编码标准。", "degree": 1, "type": "编程概念", "frequency": 2}, {"id": "任意数量的实参", "title": "任意数量的实参", "description": "任意数量的实参允许函数接受不定数量的参数，通常使用*args或**kwargs实现", "degree": 1, "type": "编程概念", "frequency": 1}, {"id": "函数名", "title": "函数名", "description": "函数名是用于标识函数的名称，应该清晰表达函数的用途", "degree": 2, "type": "编程概念", "frequency": 1}, {"id": "类", "title": "类", "description": "类是Python中的一种结构，用于封装函数和数据，实现面向对象编程", "degree": 1, "type": "类", "frequency": 1}], "edges": [{"id": "e6a55d44-5eb6-4daa-a1dc-58dc567e3436", "source": "函数", "target": "调用", "from": "函数", "to": "调用", "description": "“函数”通过“调用”机制被执行，即当函数被调用时，它会按照其定义执行相应的任务。这种调用机制是函数执行其功能的基本方式。", "weight": 18.0, "combined_degree": 31}, {"id": "1edcae85-11ef-4503-8bbc-317bccec6b0f", "source": "函数", "target": "模块", "from": "函数", "to": "模块", "description": "函数可以存储在模块中，以便于组织和重用。模块作为Python程序的基本组织单元，能够有效地封装函数，使得这些函数在多个程序中得以复用，从而提高代码的可维护性和结构清晰度。", "weight": 18.0, "combined_degree": 35}, {"id": "9d79b0fc-d456-4984-af8e-f902ee591eed", "source": "函数", "target": "GREET_USER()", "from": "函数", "to": "GREET_USER()", "description": "greet_user()是一个函数的实例", "weight": 1.0, "combined_degree": 38}, {"id": "612404b1-2d17-4402-9cbb-f9aa00484d2a", "source": "DEF", "target": "函数定义", "from": "DEF", "to": "函数定义", "description": "def关键字用于开始一个函数定义", "weight": 10.0, "combined_degree": 7}, {"id": "a58c90fd-00b5-4527-8159-727522770232", "source": "DEF", "target": "GREET_USER()", "from": "DEF", "to": "GREET_USER()", "description": "在中文Python编程教程社区中，DEF 是用于定义函数的关键字。在定义 greet_user() 函数时，开发者使用 def 关键字来声明该函数。这种用法体现了 Python 语言中函数定义的基本语法结构，即通过 def 关键字引入函数名和参数列表，从而创建可复用的功能模块。greet_user() 函数的定义示例也常被用于初学者学习函数结构的入门案例。", "weight": 20.0, "combined_degree": 13}, {"id": "873daf95-1845-4372-a98a-3255bdedc784", "source": "函数定义", "target": "函数体", "from": "函数定义", "to": "函数体", "description": "函数体是函数定义的一部分，包含具体执行代码", "weight": 10.0, "combined_degree": 4}, {"id": "fef729ba-2794-4fbe-bf66-79f26c5a718a", "source": "函数体", "target": "文档字符串", "from": "函数体", "to": "文档字符串", "description": "文档字符串通常位于函数体的开头，用于描述函数用途", "weight": 8.0, "combined_degree": 5}, {"id": "ad3bab93-1e97-43e4-87b0-f3437f9f3346", "source": "GREET_USER()", "target": "文档字符串", "from": "GREET_USER()", "to": "文档字符串", "description": "greet_user() 函数定义后紧跟文档字符串用于描述函数功能", "weight": 9.0, "combined_degree": 11}, {"id": "49e925ea-f8f4-4325-826f-03246d54238d", "source": "GREET_USER()", "target": "PRINT()", "from": "GREET_USER()", "to": "PRINT()", "description": "在中文Python编程教程社区中，greet_user() 是一个用于输出问候语的函数，其函数体内部调用了 print() 函数来实现信息的显示。这种结构展示了初学者在学习函数定义与调用时的典型用法，即通过在自定义函数 greet_user() 中嵌套调用内建函数 print()，以实现基本的输出功能。", "weight": 18.0, "combined_degree": 16}, {"id": "aeb2b9cd-e369-4fc8-8a1d-2acb36f0693b", "source": "GREET_USER()", "target": "函数调用", "from": "GREET_USER()", "to": "函数调用", "description": "函数调用用于执行 greet_user() 函数的代码", "weight": 10.0, "combined_degree": 19}, {"id": "551e4a12-e761-4d0b-882b-c9aa879ab66b", "source": "GREET_USER()", "target": "USERNAME", "from": "GREET_USER()", "to": "USERNAME", "description": "greet_user() 函数通过形参 username 接收传入的用户名，用于实现对用户的问候功能。该函数的设计使其能够根据调用时提供的 username 参数进行个性化响应。", "weight": 20.0, "combined_degree": 12}, {"id": "8541f295-04f7-4e37-9643-b5eb9bf00ac4", "source": "PRINT()", "target": "USERNAME", "from": "PRINT()", "to": "USERNAME", "description": "print() 函数输出包含 username 的问候语", "weight": 1.0, "combined_degree": 12}, {"id": "e834da38-9ca5-44d8-af2c-c81c0a5945b0", "source": "USERNAME", "target": "TITLE()", "from": "USERNAME", "to": "TITLE()", "description": "username 使用 title() 方法格式化为首字母大写形式", "weight": 8.0, "combined_degree": 10}, {"id": "4938bd67-e860-44f6-b31b-50e43bc4b95b", "source": "GREET_USER()", "target": "实参", "from": "GREET_USER()", "to": "实参", "description": "greet_user()函数在调用时接收实参作为输入", "weight": 10.0, "combined_degree": 18}, {"id": "d7e7d13f-6f80-47a2-8877-19d1f1573fa1", "source": "GREET_USER()", "target": "形参", "from": "GREET_USER()", "to": "形参", "description": "greet_user()函数定义中包含形参以接收调用时传入的值", "weight": 10.0, "combined_degree": 17}, {"id": "7d4f1a39-4ab4-4b88-862c-2b0c97fa8dba", "source": "实参", "target": "形参", "from": "实参", "to": "形参", "description": "在中文Python编程教程社区中，“实参”是指在函数调用时传递给“形参”的具体值。实参的作用是在调用函数时，将实际的数据传递给函数定义中的形参，以供函数内部使用。这种参数传递机制是函数调用过程中实现数据输入的关键步骤，有助于函数根据不同的输入执行相应的逻辑。", "weight": 21.0, "combined_degree": 19}, {"id": "f70cc5a1-a703-4eee-828a-b58a348adb47", "source": "FAVORITE_BOOK()", "target": "TITLE", "from": "FAVORITE_BOOK()", "to": "TITLE", "description": "title 是 favorite_book() 函数的形参", "weight": 10.0, "combined_degree": 2}, {"id": "eadd6b7a-1e0a-4e26-a3af-61846c0ab50e", "source": "实参", "target": "位置实参", "from": "实参", "to": "位置实参", "description": "位置实参是一种传递实参的方式", "weight": 9.0, "combined_degree": 18}, {"id": "f129f119-15bb-4dee-92aa-d8a580cf9a91", "source": "实参", "target": "关键字实参", "from": "实参", "to": "关键字实参", "description": "关键字实参是实参的一种特殊形式，它通过名称=值的方式进行传递，是另一种传递实参的方式。与位置实参不同，关键字实参通过显式指定参数名称，使得函数调用时参数的顺序不再重要，从而提高了代码的可读性和灵活性。", "weight": 19.0, "combined_degree": 19}, {"id": "e2aef597-5480-4cae-aea8-2c1f0fbcbf1d", "source": "实参", "target": "LIST", "from": "实参", "to": "LIST", "description": "列表可以作为实参传递给函数", "weight": 8.0, "combined_degree": 11}, {"id": "5f34e9aa-e0d6-47e8-b10c-5c2b9de72504", "source": "实参", "target": "DICT", "from": "实参", "to": "DICT", "description": "字典可以作为实参传递给函数", "weight": 1.0, "combined_degree": 11}, {"id": "04f44eea-f3e5-42f7-8ddf-2fd2200df211", "source": "DESCRIBE_PET()", "target": "ANIMAL_TYPE", "from": "DESCRIBE_PET()", "to": "ANIMAL_TYPE", "description": "在 describe_pet() 函数中，animal_type 是第二个形参，具有默认值。尽管它是函数的一个参数，但由于设有默认值，因此在调用 describe_pet() 时可以选择性地提供该参数。", "weight": 48.0, "combined_degree": 11}, {"id": "5500e1f9-4cc3-4b1f-a8b2-0584260e6e93", "source": "DESCRIBE_PET()", "target": "PET_NAME", "from": "DESCRIBE_PET()", "to": "PET_NAME", "description": "在中文Python编程教程社区中，describe_pet() 是一个常用于教学的函数，其第一个形参为 pet_name。pet_name 是 describe_pet() 函数的必需参数之一，作为函数的第一个形参，它在调用函数时必须被提供。该参数用于指定宠物的名称，是理解函数参数传递和定义顺序的重要示例。", "weight": 40.0, "combined_degree": 12}, {"id": "a9b3e26b-af63-44ee-8135-74f99e06eb58", "source": "DESCRIBE_PET", "target": "ANIMAL_TYPE", "from": "DESCRIBE_PET", "to": "ANIMAL_TYPE", "description": "animal_type 是 describe_pet() 函数的一个参数，用于指定宠物类型", "weight": 10.0, "combined_degree": 10}, {"id": "7a412029-4a6c-451e-81f8-d051c160703b", "source": "DESCRIBE_PET", "target": "PET_NAME", "from": "DESCRIBE_PET", "to": "PET_NAME", "description": "pet_name 是 describe_pet() 函数的一个参数，用于指定宠物名字", "weight": 10.0, "combined_degree": 11}, {"id": "5046a84e-b91e-4e6f-9585-5913bb0fff26", "source": "DESCRIBE_PET", "target": "PRINT", "from": "DESCRIBE_PET", "to": "PRINT", "description": "describe_pet() 函数内部使用 print() 函数输出宠物信息", "weight": 9.0, "combined_degree": 8}, {"id": "87f5d8c2-8fea-401d-8fcd-25a007d8cce7", "source": "DESCRIBE_PET", "target": "函数调用", "from": "DESCRIBE_PET", "to": "函数调用", "description": "describe_pet() 通过函数调用语法结构被多次调用", "weight": 8.0, "combined_degree": 16}, {"id": "1a37f7e8-3a0a-4b72-9d02-890069b6fb26", "source": "DESCRIBE_PET", "target": "DEF", "from": "DESCRIBE_PET", "to": "DEF", "description": "def 关键字用于定义 describe_pet() 函数", "weight": 1.0, "combined_degree": 10}, {"id": "ed858ba6-6522-4ec6-85a6-761240760126", "source": "PET_NAME", "target": "TITLE()", "from": "PET_NAME", "to": "TITLE()", "description": "pet_name 使用 title() 方法将宠物名字首字母大写", "weight": 9.0, "combined_degree": 12}, {"id": "e1b0776b-5a44-4862-85a3-4cac46dc7810", "source": "函数", "target": "调用函数", "from": "函数", "to": "调用函数", "description": "调用函数是执行函数定义中代码的方式", "weight": 10.0, "combined_degree": 32}, {"id": "d69f4215-ac76-4d72-b2df-9d58120124dc", "source": "函数", "target": "形参", "from": "函数", "to": "形参", "description": "在中文Python编程教程社区中，函数是程序中实现特定功能的代码块，而形参则是在函数定义中用于接收调用时传入的值的变量。函数通过形参接收调用时提供的实际参数，从而实现参数化和复用。这种机制使得函数在不同上下文中能够灵活处理不同的数据输入，是Python编程中函数设计的核心概念之一。", "weight": 19.0, "combined_degree": 39}, {"id": "3a71a030-d4cc-41bd-8515-946b358aa16b", "source": "位置实参", "target": "实参", "from": "位置实参", "to": "实参", "description": "位置实参是实参的一种形式，在函数调用时按照参数在函数定义中出现的顺序进行传递。", "weight": 18.0, "combined_degree": 18}, {"id": "76e94976-0e0e-4612-8c36-4f7a42e11aac", "source": "位置实参", "target": "形参", "from": "位置实参", "to": "形参", "description": "在Python编程中，位置实参是用于将值传递给形参的一种方式。它们按照在函数调用中出现的顺序，依次对应函数定义中的形参。这种顺序匹配机制确保了每个位置实参都能准确地赋值给相应的形参，从而实现函数的正确调用和执行。", "weight": 26.0, "combined_degree": 17}, {"id": "1f69f332-d522-4e67-9461-c3b1b2b8f71f", "source": "关键字实参", "target": "实参", "from": "关键字实参", "to": "实参", "description": "关键字实参是实参的一种形式，它通过名称=值的方式进行传递。这种形式使得在调用函数时可以明确指定每个参数的名称，从而提高代码的可读性和灵活性。", "weight": 18.0, "combined_degree": 19}, {"id": "0397f0e7-4844-4368-914b-ffff9be6fc27", "source": "关键字实参", "target": "形参", "from": "关键字实参", "to": "形参", "description": "关键字实参用于将值传递给形参，并且通过名称与形参对应。这种方式使得在调用函数时，可以明确指定每个参数的名称，从而提高代码的可读性和灵活性。", "weight": 17.0, "combined_degree": 18}, {"id": "7d845841-6f4b-4540-b5ff-7f6a0a5b7bed", "source": "调用函数", "target": "实参", "from": "调用函数", "to": "实参", "description": "调用函数时需要提供实参以传递数据", "weight": 9.0, "combined_degree": 12}, {"id": "e3d0efb8-4f46-47f3-a416-9e3e82e13c78", "source": "形参", "target": "实参", "from": "形参", "to": "实参", "description": "在Python编程中，实参是调用函数时传递给形参的值。实参可以按照顺序或通过名称的方式传递给形参，从而使函数在执行时能够接收并使用这些输入数据。形参则是在函数定义中用于接收实参的变量。两者共同构成了函数调用过程中参数传递的核心机制。", "weight": 11.0, "combined_degree": 19}, {"id": "519930ba-1a85-447e-a116-cdb47c3d86d8", "source": "PRINT()", "target": "TITLE()", "from": "PRINT()", "to": "TITLE()", "description": "print()函数中使用title()方法格式化字符串输出", "weight": 1.0, "combined_degree": 14}, {"id": "8f9b0d1e-b5aa-4b18-bc00-8836d4a8a13c", "source": "关键字实参", "target": "DESCRIBE_PET()", "from": "关键字实参", "to": "DESCRIBE_PET()", "description": "describe_pet() 函数支持使用关键字实参进行调用，这种方式允许调用者通过显式地指定参数名称来传递值，从而提高代码的可读性和灵活性。关键字实参使得在调用 describe_pet() 函数时，可以不按照参数的默认顺序传递值，而是通过参数名明确地指明每个值的含义。", "weight": 18.0, "combined_degree": 15}, {"id": "fe8d28d8-abcb-436b-ad31-f122ac8eba8d", "source": "ANIMAL_TYPE", "target": "默认值", "from": "ANIMAL_TYPE", "to": "默认值", "description": "在“中文Python编程教程”社区中，实体 ANIMAL_TYPE 通常作为函数的形参使用，并且可以被指定默认值。在相关示例中，animal_type 在函数定义中被赋予了默认值 'dog'，这说明在调用该函数时，如果未显式传入 animal_type 参数，系统将自动使用 'dog' 作为默认值。这种用法体现了 Python 中函数参数默认值的常见实践，有助于简化函数调用并提高代码的可读性。", "weight": 17.0, "combined_degree": 9}, {"id": "ffca0ae9-3db0-40a7-bbd6-2126a5dc85f9", "source": "PET_NAME", "target": "默认值", "from": "PET_NAME", "to": "默认值", "description": "可以为形参 pet_name 指定默认值", "weight": 1.0, "combined_degree": 10}, {"id": "7145ed7a-e3b3-4a96-b175-d70a0ec4a6e4", "source": "默认值", "target": "DESCRIBE_PET()", "from": "默认值", "to": "DESCRIBE_PET()", "description": "describe_pet() 函数通过为 animal_type 指定默认值实现默认值机制", "weight": 10.0, "combined_degree": 10}, {"id": "aa574100-5337-46b5-b7cc-551f682651d4", "source": "默认值", "target": "ANIMAL_TYPE", "from": "默认值", "to": "ANIMAL_TYPE", "description": "animal_type 具有默认值 'dog'，在未提供实参时使用", "weight": 10.0, "combined_degree": 9}, {"id": "32061a0d-bb7d-47a3-9356-2e4ecbe46585", "source": "PET_NAME", "target": "位置实参", "from": "PET_NAME", "to": "位置实参", "description": "在“中文Python编程教程”社区中，pet_name 通常作为位置实参传入 describe_pet() 函数。这意味着在调用 describe_pet() 函数时，pet_name 的值通常是按照参数顺序直接传递的，而不是通过关键字形式指定。这种用法反映了初学者在学习函数调用时对位置实参的常见实践。", "weight": 16.0, "combined_degree": 14}, {"id": "fa03e82d-ad99-4442-a24e-8f317de6f207", "source": "PET_NAME", "target": "F<STRING>", "from": "PET_NAME", "to": "F<STRING>", "description": "f-string 中嵌入 pet_name 变量以格式化输出宠物名字", "weight": 7.0, "combined_degree": 8}, {"id": "90549b44-3e3e-4e02-866e-9e21905207ab", "source": "ANIMAL_TYPE", "target": "F<STRING>", "from": "ANIMAL_TYPE", "to": "F<STRING>", "description": "f-string 中嵌入 animal_type 变量以格式化输出宠物种类", "weight": 1.0, "combined_degree": 7}, {"id": "9e3693ee-54b8-481a-9745-5a4b4b425a6d", "source": "位置实参", "target": "函数调用", "from": "位置实参", "to": "函数调用", "description": "函数调用中实参按顺序传递给形参，形成位置实参", "weight": 8.0, "combined_degree": 19}, {"id": "2a8a423d-de52-4574-8623-15269f05aa71", "source": "ANIMAL_TYPE", "target": "关键字实参", "from": "ANIMAL_TYPE", "to": "关键字实参", "description": "animal_type 可以通过关键字实参显式传递", "weight": 8.0, "combined_degree": 14}, {"id": "877812a8-fa7b-443c-a7d1-a91ea7c1dd48", "source": "默认值", "target": "形参", "from": "默认值", "to": "形参", "description": "默认值是形参的一种属性，用于在未提供实参时使用", "weight": 9.0, "combined_degree": 13}, {"id": "0ce8c0f4-a117-43f3-ad05-06021a2c04e0", "source": "位置实参", "target": "DESCRIBE_PET()", "from": "位置实参", "to": "DESCRIBE_PET()", "description": "describe_pet() 函数可以通过位置实参方式传递参数", "weight": 9.0, "combined_degree": 14}, {"id": "f1aa8123-8ce2-48a5-93bb-5eec5d183970", "source": "DESCRIBE_PET()", "target": "TYPEERROR", "from": "DESCRIBE_PET()", "to": "TYPEERROR", "description": "当调用 describe_pet() 时未提供必要的实参，会引发 TypeError", "weight": 10.0, "combined_degree": 8}, {"id": "b4deeb0e-f6b6-4384-8cfe-d881a241f002", "source": "TYPEERROR", "target": "TRACEBACK", "from": "TYPEERROR", "to": "TRACEBACK", "description": "TypeError 异常会通过 traceback 显示错误信息和调用栈", "weight": 8.0, "combined_degree": 6}, {"id": "b7215768-3575-4aa6-90cb-b042e786ed54", "source": "MAKE_SHIRT()", "target": "位置实参", "from": "MAKE_SHIRT()", "to": "位置实参", "description": "make_shirt() 函数可以使用位置实参进行调用", "weight": 9.0, "combined_degree": 10}, {"id": "c6b6ce4b-515b-4907-8f0e-d5c3c5927652", "source": "MAKE_SHIRT()", "target": "关键字实参", "from": "MAKE_SHIRT()", "to": "关键字实参", "description": "make_shirt() 函数可以使用关键字实参进行调用", "weight": 9.0, "combined_degree": 11}, {"id": "50476412-c0f7-4300-8db1-bb405d2937dd", "source": "TRACEBACK", "target": "函数调用", "from": "TRACEBACK", "to": "函数调用", "description": "traceback 显示函数调用中发生错误的位置", "weight": 8.0, "combined_degree": 15}, {"id": "b901e317-df48-4492-84f7-65216de32348", "source": "TRACEBACK", "target": "实参", "from": "TRACEBACK", "to": "实参", "description": "traceback 报告实参数量不匹配等错误", "weight": 9.0, "combined_degree": 14}, {"id": "a4569202-4f98-482c-bd33-7c8338604a03", "source": "TRACEBACK", "target": "形参", "from": "TRACEBACK", "to": "形参", "description": "traceback 指出缺失的形参名称", "weight": 9.0, "combined_degree": 13}, {"id": "7ffdc347-3522-40f6-b5ec-0a69c173af18", "source": "形参", "target": "函数调用", "from": "形参", "to": "函数调用", "description": "函数调用中的实参必须与形参匹配", "weight": 1.0, "combined_degree": 20}, {"id": "13f6725b-8b6d-4bbf-accc-9410867485aa", "source": "实参", "target": "函数调用", "from": "实参", "to": "函数调用", "description": "函数调用时需要提供实参以匹配形参", "weight": 10.0, "combined_degree": 21}, {"id": "57d87d32-4eda-446b-b026-0756483f2878", "source": "RETURN", "target": "GET_FORMATTED_NAME()", "from": "RETURN", "to": "GET_FORMATTED_NAME()", "description": "get_formatted_name() 使用 return 关键字将格式化后的姓名返回", "weight": 10.0, "combined_degree": 15}, {"id": "7d578a2f-054e-4295-b571-fac8323270f9", "source": "RETURN", "target": "返回值", "from": "RETURN", "to": "返回值", "description": "return 关键字用于生成函数的返回值", "weight": 10.0, "combined_degree": 5}, {"id": "97ec7ef1-a7ef-4b3b-9594-6bd2119b210c", "source": "GET_FORMATTED_NAME()", "target": "FULL_NAME", "from": "GET_FORMATTED_NAME()", "to": "FULL_NAME", "description": "get_formatted_name() 函数中定义了 full_name 变量用于存储格式化结果", "weight": 9.0, "combined_degree": 15}, {"id": "52a6f876-bf2a-4b0f-9afb-6008cb76ac4c", "source": "FULL_NAME", "target": "TITLE()", "from": "FULL_NAME", "to": "TITLE()", "description": "full_name 使用 title() 方法将姓名格式化为首字母大写", "weight": 10.0, "combined_degree": 9}, {"id": "6be21da8-97fe-44df-a3ea-5f74f4190c0a", "source": "FULL_NAME", "target": "GET_FORMATTED_NAME()", "from": "FULL_NAME", "to": "GET_FORMATTED_NAME()", "description": "get_formatted_name() 函数内部创建 full_name 变量以存储格式化后的姓名", "weight": 9.0, "combined_degree": 15}, {"id": "dea969f9-d482-44f9-b436-9dfd05297706", "source": "FULL_NAME", "target": "F-STRING", "from": "FULL_NAME", "to": "F-STRING", "description": "full_name 使用 f-string 表达式拼接名、中间名和姓", "weight": 9.0, "combined_degree": 5}, {"id": "4a2fd106-b7b1-469f-84b3-478ecd9180b3", "source": "MUSICIAN", "target": "GET_FORMATTED_NAME()", "from": "MUSICIAN", "to": "GET_FORMATTED_NAME()", "description": "get_formatted_name() 的返回值被赋给变量 musician", "weight": 9.0, "combined_degree": 17}, {"id": "07ef9e9a-dbf7-48ce-b2b6-7e16f4fd3cf2", "source": "MUSICIAN", "target": "PRINT()", "from": "MUSICIAN", "to": "PRINT()", "description": "musician 变量作为参数传递给 print() 函数以输出格式化姓名", "weight": 1.0, "combined_degree": 13}, {"id": "4ab2a51b-a1b8-4af5-9fb9-eb06306de692", "source": "GET_FORMATTED_NAME()", "target": "RETURN", "from": "GET_FORMATTED_NAME()", "to": "RETURN", "description": "get_formatted_name() 函数使用 return 关键字将格式化后的姓名返回。该函数通过 return 实现将处理后的姓名值传递给调用者，是 Python 中常见的函数返回值用法示例。", "weight": 17.0, "combined_degree": 15}, {"id": "e2444a37-7d95-4061-963e-509d62a83b26", "source": "GET_FORMATTED_NAME()", "target": "DEF", "from": "GET_FORMATTED_NAME()", "to": "DEF", "description": "def 关键字用于定义 get_formatted_name() 函数", "weight": 10.0, "combined_degree": 17}, {"id": "fc8af312-e297-40d9-87bd-8a5f82c60b0a", "source": "GET_FORMATTED_NAME()", "target": "DEFAULT VALUE", "from": "GET_FORMATTED_NAME()", "to": "DEFAULT VALUE", "description": "get_formatted_name() 使用默认值使 middle_name 参数变为可选", "weight": 9.0, "combined_degree": 13}, {"id": "2a728773-4d88-4c8b-afd1-1ff5e85145fd", "source": "GET_FORMATTED_NAME()", "target": "IF", "from": "GET_FORMATTED_NAME()", "to": "IF", "description": "get_formatted_name() 使用 if 语法结构判断是否提供了中间名", "weight": 8.0, "combined_degree": 18}, {"id": "50836853-5c35-45db-8e69-9156b276709b", "source": "GET_FORMATTED_NAME()", "target": "F-STRING", "from": "GET_FORMATTED_NAME()", "to": "F-STRING", "description": "get_formatted_name() 使用 f-string 拼接姓名字符串", "weight": 9.0, "combined_degree": 14}, {"id": "e5e0c99a-83f3-46d5-91c0-cf16cb57710d", "source": "GET_FORMATTED_NAME()", "target": "TITLE()", "from": "GET_FORMATTED_NAME()", "to": "TITLE()", "description": "get_formatted_name() 函数通过调用 title() 方法，将输入的姓名格式化为首字母大写的形式。该函数的核心功能是利用 title() 方法对姓名进行标准化处理，使其符合常见的书写规范。", "weight": 16.0, "combined_degree": 18}, {"id": "e4f7380e-fc63-4fce-845e-52b38120191d", "source": "GET_FORMATTED_NAME()", "target": "MUSICIAN", "from": "GET_FORMATTED_NAME()", "to": "MUSICIAN", "description": "musician 变量用于接收 get_formatted_name() 的返回值", "weight": 1.0, "combined_degree": 17}, {"id": "1ff0e131-2e28-4c7b-8310-d730d8042b20", "source": "IF", "target": "ELSE", "from": "IF", "to": "ELSE", "description": "if和else语法结构共同用于条件判断和分支执行", "weight": 10.0, "combined_degree": 7}, {"id": "ed10b1f5-8531-4d21-a509-470aa7abb805", "source": "IF", "target": "MIDDLE_NAME", "from": "IF", "to": "MIDDLE_NAME", "description": "if语句使用middle_name变量判断是否提供中间名", "weight": 9.0, "combined_degree": 7}, {"id": "66d89e18-47a3-465d-a512-7c1d371af7d7", "source": "函数调用", "target": "MUSICIAN", "from": "函数调用", "to": "MUSICIAN", "description": "函数调用的返回值被赋给musician变量", "weight": 8.0, "combined_degree": 16}, {"id": "ea478b95-bf31-40f5-af90-1063d6b858b2", "source": "函数调用", "target": "BUILD_PERSON", "from": "函数调用", "to": "BUILD_PERSON", "description": "build_person是被调用的函数，返回值用于后续处理", "weight": 9.0, "combined_degree": 15}, {"id": "63147be9-e431-409c-802d-08777de6f490", "source": "MUSICIAN", "target": "打印", "from": "MUSICIAN", "to": "打印", "description": "musician变量的值被打印语句输出", "weight": 7.0, "combined_degree": 6}, {"id": "b90d0ef8-332f-4bd6-8d35-d3f0597f2e16", "source": "MUSICIAN", "target": "BUILD_PERSON", "from": "MUSICIAN", "to": "BUILD_PERSON", "description": "build_person函数的返回值被赋给musician变量", "weight": 8.0, "combined_degree": 9}, {"id": "9aac73a4-4acc-4c28-b022-faac13e93106", "source": "形参", "target": "BUILD_PERSON", "from": "形参", "to": "BUILD_PERSON", "description": "build_person函数定义中包含形参用于接收姓名信息", "weight": 8.0, "combined_degree": 13}, {"id": "1c4aaf7b-bd88-4952-822d-3c1c7603ebf4", "source": "BUILD_PERSON", "target": "字典", "from": "BUILD_PERSON", "to": "字典", "description": "build_person函数返回一个包含姓名信息的字典", "weight": 10.0, "combined_degree": 14}, {"id": "8439e8c2-9a4e-45ac-81b4-4514eca90ad7", "source": "字典", "target": "数据结构", "from": "字典", "to": "数据结构", "description": "字典是Python中常用的数据结构之一", "weight": 10.0, "combined_degree": 12}, {"id": "241e1e94-f952-4ac3-b8ba-881c79f66228", "source": "列表", "target": "数据结构", "from": "列表", "to": "数据结构", "description": "列表是Python中常用的数据结构之一", "weight": 1.0, "combined_degree": 11}, {"id": "0f68ce88-fb72-4055-b64f-4bee9ed56fa9", "source": "BUILD_PERSON()", "target": "字典", "from": "BUILD_PERSON()", "to": "字典", "description": "build_person() 函数返回一个包含个人信息的字典", "weight": 10.0, "combined_degree": 16}, {"id": "9033aa0b-013e-4c3c-be84-7b7864c65b33", "source": "BUILD_PERSON()", "target": "FIRST_NAME", "from": "BUILD_PERSON()", "to": "FIRST_NAME", "description": "build_person() 函数使用 first_name 参数作为字典的 'first' 键的值", "weight": 9.0, "combined_degree": 7}, {"id": "1f10372b-fb10-4240-835c-e63b788bbc97", "source": "BUILD_PERSON()", "target": "LAST_NAME", "from": "BUILD_PERSON()", "to": "LAST_NAME", "description": "build_person() 函数使用 last_name 参数作为字典的 'last' 键的值", "weight": 9.0, "combined_degree": 7}, {"id": "7b30846e-63f5-4e07-9b87-9b7dee981afe", "source": "BUILD_PERSON()", "target": "AGE", "from": "BUILD_PERSON()", "to": "AGE", "description": "build_person() 函数可选地使用 age 参数将年龄添加到字典中", "weight": 8.0, "combined_degree": 10}, {"id": "3ce0561a-7ed7-4f85-bfa9-d9e3dd514274", "source": "BUILD_PERSON()", "target": "RETURN", "from": "BUILD_PERSON()", "to": "RETURN", "description": "return 语句用于将构建好的字典从 build_person() 函数返回", "weight": 10.0, "combined_degree": 9}, {"id": "170256ff-db6b-49ae-829f-9d2c44dc5156", "source": "BUILD_PERSON()", "target": "PRINT", "from": "BUILD_PERSON()", "to": "PRINT", "description": "print() 函数用于输出 build_person() 函数返回的字典", "weight": 1.0, "combined_degree": 9}, {"id": "77a0c1a5-dc3b-472b-8769-e6c072048d34", "source": "AGE", "target": "NONE", "from": "AGE", "to": "NONE", "description": "age 参数的默认值为 None，表示未提供值", "weight": 9.0, "combined_degree": 6}, {"id": "085c465a-c1a3-4755-b1db-a36e08c569dd", "source": "AGE", "target": "IF", "from": "AGE", "to": "IF", "description": "if 语句用于检查是否提供了 age 参数", "weight": 8.0, "combined_degree": 10}, {"id": "68b41670-3826-4bc7-8ad5-459e61328078", "source": "NONE", "target": "FALSE", "from": "NONE", "to": "FALSE", "description": "在条件测试中，None 被视为 False", "weight": 8.0, "combined_degree": 3}, {"id": "86fc79e1-25a2-47fe-a02d-fceb2ad71182", "source": "AGE", "target": "字典", "from": "AGE", "to": "字典", "description": "age 变量的值被存储到字典中作为键值对", "weight": 7.0, "combined_degree": 14}, {"id": "e05444a0-934a-423b-a4f8-5b230757dfe0", "source": "函数", "target": "GET_FORMATTED_NAME()", "from": "函数", "to": "GET_FORMATTED_NAME()", "description": "get_formatted_name() 是一个函数的具体实现", "weight": 10.0, "combined_degree": 42}, {"id": "faff4361-179a-4dcd-8bce-fabfff94fa95", "source": "GET_FORMATTED_NAME()", "target": "WHILE", "from": "GET_FORMATTED_NAME()", "to": "WHILE", "description": "get_formatted_name() 函数在 while 循环中被调用以持续获取用户输入", "weight": 9.0, "combined_degree": 18}, {"id": "afc0da5b-f861-4f54-a4c2-09589cb18103", "source": "GET_FORMATTED_NAME()", "target": "F_STRING", "from": "GET_FORMATTED_NAME()", "to": "F_STRING", "description": "get_formatted_name() 函数中使用 f-string 拼接姓名", "weight": 1.0, "combined_degree": 13}, {"id": "8148a01a-0d72-4ada-959e-c365138de8d3", "source": "WHILE", "target": "BREAK", "from": "WHILE", "to": "BREAK", "description": "break 用于终止 while 循环，提供退出机制", "weight": 10.0, "combined_degree": 7}, {"id": "9ead6d23-fdde-4281-b502-a9bedbbddddd", "source": "WHILE", "target": "INPUT()", "from": "WHILE", "to": "INPUT()", "description": "input() 在 while 循环中用于获取用户输入", "weight": 9.0, "combined_degree": 8}, {"id": "096354a2-33fc-42ca-83d9-b16dbaccb5f0", "source": "BREAK", "target": "WHILE", "from": "BREAK", "to": "WHILE", "description": "break 用于在 while 循环中根据条件提前终止循环", "weight": 9.0, "combined_degree": 7}, {"id": "fa598af6-619f-42bb-8f11-ca9588b3709b", "source": "INPUT()", "target": "IF", "from": "INPUT()", "to": "IF", "description": "input() 的返回值常用于 if 条件判断中以控制程序流程", "weight": 8.0, "combined_degree": 8}, {"id": "1eebd509-47b8-4432-961f-754ed68c1d84", "source": "GET_FORMATTED_NAME()", "target": "F字符串", "from": "GET_FORMATTED_NAME()", "to": "F字符串", "description": "get_formatted_name() 使用 f 字符串拼接姓和名", "weight": 8.0, "combined_degree": 16}, {"id": "6a771eb9-8d8d-4ede-8209-ed7385c8907a", "source": "F字符串", "target": "TITLE()", "from": "F字符串", "to": "TITLE()", "description": "在中文Python编程教程社区中，F字符串常用于字符串格式化，其中嵌入了对 title() 方法的调用，以实现对用户名等字符串的格式化处理。通过在 F 字符串中直接嵌入表达式并调用 title() 方法，用户可以将嵌入的字符串首字母大写，从而提升输出文本的可读性和规范性。这种用法在教程中被广泛用于展示字符串处理的简洁语法和实用技巧。", "weight": 15.0, "combined_degree": 10}, {"id": "f82b28c8-d2d7-4ade-a61d-331f564cc3ce", "source": "F字符串", "target": "CITY_COUNTRY()", "from": "F字符串", "to": "CITY_COUNTRY()", "description": "city_country() 函数使用 f 字符串格式化城市和国家", "weight": 8.0, "combined_degree": 5}, {"id": "8c389526-64a5-4351-8583-b78088998027", "source": "MAKE_ALBUM()", "target": "字典", "from": "MAKE_ALBUM()", "to": "字典", "description": "MAKE_ALBUM() 函数返回一个包含专辑信息的字典，其中包括歌手名和专辑名。这使得该函数在组织和存储音乐专辑相关数据时具有实用价值。", "weight": 10.0, "combined_degree": 12}, {"id": "8bdf2aa2-75ff-4905-8af0-6a0ac3c0f599", "source": "MAKE_ALBUM()", "target": "WHILE", "from": "MAKE_ALBUM()", "to": "WHILE", "description": "while 循环用于反复调用 make_album() 函数以获取用户输入\nwhile 循环中调用 make_album() 函数以持续获取用户输入", "weight": 16.0, "combined_degree": 8}, {"id": "a108216e-8cac-4b27-b655-b5764061be37", "source": "GREET_USERS()", "target": "列表", "from": "GREET_USERS()", "to": "列表", "description": "greet_users() 函数接收一个名字列表作为参数", "weight": 9.0, "combined_degree": 13}, {"id": "6ee1bca2-77de-4957-96f6-2471e205345b", "source": "GREET_USERS()", "target": "FOR", "from": "GREET_USERS()", "to": "FOR", "description": "greet_users() 函数内部使用 for 循环遍历名字列表", "weight": 9.0, "combined_degree": 11}, {"id": "d78ea8ef-f15d-4237-8130-7199d9252a48", "source": "列表", "target": "FOR", "from": "列表", "to": "FOR", "description": "for 循环用于遍历列表中的每个元素", "weight": 10.0, "combined_degree": 16}, {"id": "6c8500e5-7cc0-4a02-9c3f-011b16108bbf", "source": "列表", "target": "NAMES", "from": "列表", "to": "NAMES", "description": "names 是一个字符串列表，作为 greet_users() 的输入", "weight": 1.0, "combined_degree": 12}, {"id": "6a9155ef-5683-416a-ad24-cbfa66ee14c6", "source": "GREET_USERS()", "target": "NAMES", "from": "GREET_USERS()", "to": "NAMES", "description": "greet_users() 函数接受 names 作为参数", "weight": 10.0, "combined_degree": 7}, {"id": "69c1a482-1a06-4ffd-ad01-04558bbb490a", "source": "GREET_USERS()", "target": "USERNAME", "from": "GREET_USERS()", "to": "USERNAME", "description": "usernames 列表被传递给 greet_users() 函数作为参数", "weight": 10.0, "combined_degree": 8}, {"id": "7441f418-3e0c-40b2-867f-3a60e74df001", "source": "NAMES", "target": "FOR", "from": "NAMES", "to": "FOR", "description": "for 循环用于遍历 names 列表中的每个元素", "weight": 9.0, "combined_degree": 10}, {"id": "d195e7db-42b5-44ee-8789-faf43c3ddc8d", "source": "F字符串", "target": "PRINT()", "from": "F字符串", "to": "PRINT()", "description": "f字符串被传递给 print() 函数以输出格式化的问候语", "weight": 9.0, "combined_degree": 12}, {"id": "e2ec7e58-46c6-4d16-a82b-e12bfa2dbe83", "source": "PRINT()", "target": "COMPLETED_MODELS", "from": "PRINT()", "to": "COMPLETED_MODELS", "description": "print() 函数用于输出 completed_models 列表中的内容", "weight": 1.0, "combined_degree": 14}, {"id": "aefe5e14-cad2-4377-a6d6-e316012aba8b", "source": "UNPRINTED_DESIGNS", "target": "WHILE", "from": "UNPRINTED_DESIGNS", "to": "WHILE", "description": "while 循环用于遍历 unprinted_designs 列表直到为空", "weight": 9.0, "combined_degree": 10}, {"id": "65a27ec7-95f2-487a-acc1-e5a8d4d5b1fc", "source": "UNPRINTED_DESIGNS", "target": "POP()", "from": "UNPRINTED_DESIGNS", "to": "POP()", "description": "pop() 方法用于从 unprinted_designs 列表中移除当前设计", "weight": 10.0, "combined_degree": 6}, {"id": "213fe078-0255-4254-af02-5e029b9c728d", "source": "COMPLETED_MODELS", "target": "APPEND()", "from": "COMPLETED_MODELS", "to": "APPEND()", "description": "append() 方法用于将当前设计添加到 completed_models 列表中", "weight": 10.0, "combined_degree": 7}, {"id": "197fb01b-116b-4f44-8989-6392a76be827", "source": "WHILE", "target": "UNPRINTED_DESIGNS", "from": "WHILE", "to": "UNPRINTED_DESIGNS", "description": "while循环使用unprinted_designs作为条件判断是否继续执行", "weight": 9.0, "combined_degree": 10}, {"id": "544c7ede-83ab-4de6-9772-7759a98067fc", "source": "PRINT()", "target": "CURRENT_DESIGN", "from": "PRINT()", "to": "CURRENT_DESIGN", "description": "print()函数用于输出当前打印的设计项current_design", "weight": 8.0, "combined_degree": 10}, {"id": "65463f80-5ae1-4f18-be33-72939d942f45", "source": "APPEND()", "target": "COMPLETED_MODELS", "from": "APPEND()", "to": "COMPLETED_MODELS", "description": "append()方法用于将打印完成的设计添加到completed_models列表中", "weight": 10.0, "combined_degree": 7}, {"id": "384acff5-e3b4-4ba3-b64d-bc097348a15b", "source": "POP()", "target": "UNPRINTED_DESIGNS", "from": "POP()", "to": "UNPRINTED_DESIGNS", "description": "pop()方法用于从unprinted_designs中移除一个设计项", "weight": 10.0, "combined_degree": 6}, {"id": "bee4dfe5-fb75-49fc-bbf4-bae0f283d5ee", "source": "POP()", "target": "CURRENT_DESIGN", "from": "POP()", "to": "CURRENT_DESIGN", "description": "current_design通过pop()方法从unprinted_designs中获取值", "weight": 9.0, "combined_degree": 4}, {"id": "be8c1a9d-48b8-4c11-9b59-f82b3e1b3f0b", "source": "UNPRINTED_DESIGNS", "target": "PRINT_MODELS()", "from": "UNPRINTED_DESIGNS", "to": "PRINT_MODELS()", "description": "print_models()函数以unprinted_designs作为输入参数", "weight": 8.0, "combined_degree": 10}, {"id": "a2021c62-a85f-49b1-a1e2-5225c53e5504", "source": "COMPLETED_MODELS", "target": "FOR", "from": "COMPLETED_MODELS", "to": "FOR", "description": "for循环用于遍历completed_models列表中的每个设计项", "weight": 9.0, "combined_degree": 13}, {"id": "1013f7a9-9fef-4758-9561-451a09828e16", "source": "COMPLETED_MODELS", "target": "PRINT_MODELS()", "from": "COMPLETED_MODELS", "to": "PRINT_MODELS()", "description": "print_models()函数以completed_models作为输入参数并修改其内容", "weight": 9.0, "combined_degree": 12}, {"id": "2c7bbb83-dfa2-4f5d-b17f-a6da266afcc9", "source": "COMPLETED_MODELS", "target": "SHOW_COMPLETED_MODELS()", "from": "COMPLETED_MODELS", "to": "SHOW_COMPLETED_MODELS()", "description": "show_completed_models()函数以completed_models作为输入参数用于显示内容", "weight": 9.0, "combined_degree": 10}, {"id": "3732d90b-e27c-4e5f-9b43-bf31e57494d6", "source": "PRINT_MODELS()", "target": "SHOW_COMPLETED_MODELS", "from": "PRINT_MODELS()", "to": "SHOW_COMPLETED_MODELS", "description": "print_models()函数执行后，show_completed_models()函数用于展示结果", "weight": 1.0, "combined_degree": 7}, {"id": "864586b6-05e3-4106-9079-8e311d56b0a7", "source": "PRINT_MODELS()", "target": "UNPRINTED_DESIGNS", "from": "PRINT_MODELS()", "to": "UNPRINTED_DESIGNS", "description": "print_models() 函数用于处理设计任务，它从 unprinted_designs 列表中取出设计进行处理。为了避免对原始数据的修改，print_models() 接收的是 unprinted_designs 列表的副本作为参数。", "weight": 17.0, "combined_degree": 10}, {"id": "fe589b0b-af04-4b86-8590-052490dc2f04", "source": "PRINT_MODELS()", "target": "COMPLETED_MODELS", "from": "PRINT_MODELS()", "to": "COMPLETED_MODELS", "description": "print_models() 是一个函数，用于处理模型打印任务。该函数在执行过程中会将已完成打印的设计模型添加到 completed_models 列表中，从而修改该列表以存储所有打印完成的模型。这表明 print_models() 与 completed_models 之间存在直接的数据交互关系，completed_models 作为一个用于记录打印完成模型的集合，在函数执行过程中被动态更新。", "weight": 17.0, "combined_degree": 12}, {"id": "17436a4f-0a8e-43b8-97c3-4d98ff608ac8", "source": "SHOW_COMPLETED_MODELS()", "target": "COMPLETED_MODELS", "from": "SHOW_COMPLETED_MODELS()", "to": "COMPLETED_MODELS", "description": "show_completed_models() 函数遍历 completed_models 列表并打印每个模型", "weight": 9.0, "combined_degree": 10}, {"id": "795b1d5e-965c-49a9-b4c7-4a8958262395", "source": "UNPRINTED_DESIGNS", "target": "COMPLETED_MODELS", "from": "UNPRINTED_DESIGNS", "to": "COMPLETED_MODELS", "description": "unprinted_designs 中的元素被移动到 completed_models 中，表示状态的转变", "weight": 1.0, "combined_degree": 10}, {"id": "81c1157e-d8bd-4e34-bbe5-7f29446ceeb6", "source": "PRINT_MODELS()", "target": "SHOW_COMPLETED_MODELS()", "from": "PRINT_MODELS()", "to": "SHOW_COMPLETED_MODELS()", "description": "print_models() 和 show_completed_models() 分别负责打印设计和显示已打印模型，体现了函数职责单一的原则", "weight": 9.0, "combined_degree": 10}, {"id": "48795969-1a43-4b96-af16-3144a7317cc4", "source": "PRINT_MODELS()", "target": "函数", "from": "PRINT_MODELS()", "to": "函数", "description": "print_models() 是一个函数的具体实现", "weight": 10.0, "combined_degree": 36}, {"id": "3dc0e032-f9ee-469b-946f-85aaf5e5cf7f", "source": "PRINT_MODELS()", "target": "列表", "from": "PRINT_MODELS()", "to": "列表", "description": "print_models() 接收一个设计的列表作为输入", "weight": 9.0, "combined_degree": 15}, {"id": "026b110c-aec9-48b8-8686-62ee46e2f326", "source": "SHOW_COMPLETED_MODELS()", "target": "函数", "from": "SHOW_COMPLETED_MODELS()", "to": "函数", "description": "show_completed_models() 是一个函数的具体实现", "weight": 10.0, "combined_degree": 34}, {"id": "11371e20-e4f9-408c-8349-c083fc09a8ef", "source": "SHOW_COMPLETED_MODELS()", "target": "列表", "from": "SHOW_COMPLETED_MODELS()", "to": "列表", "description": "show_completed_models() 接收一个打印好的模型列表作为输入", "weight": 9.0, "combined_degree": 13}, {"id": "166fd099-982d-4f37-a3e0-5e5d8511a25d", "source": "函数", "target": "任务分解", "from": "函数", "to": "任务分解", "description": "函数的设计应遵循任务分解原则，每个函数只负责一项具体工作", "weight": 1.0, "combined_degree": 31}, {"id": "6909b949-212f-42d5-9343-2dff58163877", "source": "列表", "target": "修改", "from": "列表", "to": "修改", "description": "修改操作可以改变列表中的元素或结构", "weight": 8.0, "combined_degree": 10}, {"id": "bf78a7a0-0c5c-448b-9c2e-1501b911a638", "source": "函数", "target": "[:]", "from": "函数", "to": "[:]", "description": "[:] 表达式用于向函数传递列表副本以避免修改原始数据", "weight": 9.0, "combined_degree": 33}, {"id": "7140e845-ad87-48f5-908c-3d9f07c4e01e", "source": "函数", "target": "列表", "from": "函数", "to": "列表", "description": "函数可以接收列表作为参数，并对其进行操作，也可以将列表作为输入或输出进行处理。这种能力使得函数在处理数据集合时非常灵活，常用于对列表中的元素进行遍历、筛选、修改或生成新的列表结果。", "weight": 9.0, "combined_degree": 39}, {"id": "25860cf3-8614-4d24-88f7-fb1663cf8a66", "source": "列表", "target": "[:]", "from": "列表", "to": "[:]", "description": "[:] 表达式用于创建列表的副本", "weight": 9.0, "combined_degree": 12}, {"id": "6e0ccc30-c370-4ecd-a222-e80d0fbf1e5d", "source": "切片表示法", "target": "[:]", "from": "切片表示法", "to": "[:]", "description": "[:] 是切片表示法的一种形式，用于复制整个列表", "weight": 10.0, "combined_degree": 4}, {"id": "c2263b95-46be-403c-b458-6ed1a0f4b37e", "source": "SHOW_MESSAGES()", "target": "SEND_MESSAGES()", "from": "SHOW_MESSAGES()", "to": "SEND_MESSAGES()", "description": "send_messages() 是对 show_messages() 的扩展，除了打印消息，还将消息移动到另一个列表", "weight": 8.0, "combined_degree": 3}, {"id": "9417626b-2287-47c7-b750-37117fbb845b", "source": "SEND_MESSAGES()", "target": "SENT_MESSAGES", "from": "SEND_MESSAGES()", "to": "SENT_MESSAGES", "description": "send_messages() 函数将消息从原始列表移动到 sent_messages 列表中", "weight": 9.0, "combined_degree": 3}, {"id": "8c9217ec-c97e-4393-8ab8-b48b136358cf", "source": "MAKE_PIZZA()", "target": "*TOPPINGS", "from": "MAKE_PIZZA()", "to": "*TOPPINGS", "description": "make_pizza() 函数使用 *toppings 语法结构来接收任意数量的配料", "weight": 10.0, "combined_degree": 14}, {"id": "0bd1ac28-7e95-444e-b39d-6f9ac49df856", "source": "TOPPINGS", "target": "*TOPPINGS", "from": "TOPPINGS", "to": "*TOPPINGS", "description": "*toppings 语法结构创建了一个名为 toppings 的元组", "weight": 1.0, "combined_degree": 10}, {"id": "819eb775-8fe2-460b-93c4-b72230fffe92", "source": "MAKE_PIZZA", "target": "*TOPPINGS", "from": "MAKE_PIZZA", "to": "*TOPPINGS", "description": "make_pizza 函数使用 *toppings 语法结构来接收任意数量的实参，从而能够收集任意数量的配料。这种设计使得调用者可以根据需要传入多个配料参数，提升了函数的灵活性和通用性。", "weight": 20.0, "combined_degree": 16}, {"id": "9aa9261e-930d-4f50-b395-07c5369aabdb", "source": "MAKE_PIZZA", "target": "PRINT()", "from": "MAKE_PIZZA", "to": "PRINT()", "description": "make_pizza 函数内部调用 print() 输出配料信息", "weight": 9.0, "combined_degree": 16}, {"id": "e09c8d94-203c-47f1-a417-f823614b235d", "source": "MAKE_PIZZA", "target": "FOR", "from": "MAKE_PIZZA", "to": "FOR", "description": "make_pizza 函数使用 for 循环遍历 toppings 元组中的配料", "weight": 9.0, "combined_degree": 15}, {"id": "7d71fea8-07a3-4f00-904f-052b500006a3", "source": "*TOPPINGS", "target": "TOPPINGS", "from": "*TOPPINGS", "to": "TOPPINGS", "description": "*toppings 语法结构将所有传入的实参封装为一个名为 toppings 的元组", "weight": 10.0, "combined_degree": 10}, {"id": "8de561d5-87fc-412d-89ad-26acd8f878c3", "source": "*TOPPINGS", "target": "位置实参", "from": "*TOPPINGS", "to": "位置实参", "description": "位置实参在函数定义中通过 *toppings 被收集为元组", "weight": 8.0, "combined_degree": 16}, {"id": "448d9a38-9730-40d3-aa0e-060a152e0985", "source": "*TOPPINGS", "target": "关键字实参", "from": "*TOPPINGS", "to": "关键字实参", "description": "关键字实参在函数定义中也可被 *toppings 捕获为元组的一部分", "weight": 1.0, "combined_degree": 17}, {"id": "6a6696dc-886d-4d4b-9cbd-a3e53df8e7a4", "source": "TOPPINGS", "target": "元组", "from": "TOPPINGS", "to": "元组", "description": "toppings 是一个元组，包含所有传入的配料参数", "weight": 10.0, "combined_degree": 4}, {"id": "e6d463a3-dc79-4cfd-9736-56ae7dc40f10", "source": "MAKE_PIZZA", "target": "SIZE", "from": "MAKE_PIZZA", "to": "SIZE", "description": "make_pizza函数的第一个参数是size，用于指定比萨的尺寸", "weight": 10.0, "combined_degree": 9}, {"id": "5410af44-7c2b-4f68-93cc-c207ca41b2cc", "source": "*TOPPINGS", "target": "元组", "from": "*TOPPINGS", "to": "元组", "description": "*toppings在函数内部被存储为一个元组", "weight": 9.0, "combined_degree": 10}, {"id": "5137a66f-6045-4979-819c-a0b996dddbc5", "source": "*TOPPINGS", "target": "FOR", "from": "*TOPPINGS", "to": "FOR", "description": "for语法结构用于遍历*toppings中的每个配料", "weight": 9.0, "combined_degree": 15}, {"id": "44b102ab-0e35-4ee6-958f-c7aa165dae65", "source": "*TOPPINGS", "target": "*ARGS", "from": "*TOPPINGS", "to": "*ARGS", "description": "*args是*toppings的通用命名形式，具有相同的功能", "weight": 1.0, "combined_degree": 9}, {"id": "1f1bb1aa-2da8-48fb-b8f5-81041fcbad96", "source": "FOR", "target": "PRINT", "from": "FOR", "to": "PRINT", "description": "print函数在for循环中用于输出每个配料", "weight": 8.0, "combined_degree": 10}, {"id": "09f99b3e-4674-47f0-8abf-f24e84b16977", "source": "BUILD_PROFILE()", "target": "**USER_INFO", "from": "BUILD_PROFILE()", "to": "**USER_INFO", "description": "build_profile() 使用 **user_info 来接收任意数量的关键字实参", "weight": 10.0, "combined_degree": 9}, {"id": "7e7da7b4-b633-4b4a-b137-502c87fb0b63", "source": "BUILD_PROFILE()", "target": "字典", "from": "BUILD_PROFILE()", "to": "字典", "description": "BUILD_PROFILE() 函数是一个用于创建用户信息的函数，其内部逻辑会构建并返回一个包含用户信息的字典。这个字典封装了与用户相关的各类数据，是在函数内部动态生成的，用于后续的数据处理或展示。", "weight": 17.0, "combined_degree": 17}, {"id": "2f245695-1f4b-4366-87f0-8fa64c14648a", "source": "BUILD_PROFILE()", "target": "USER_PROFILE", "from": "BUILD_PROFILE()", "to": "USER_PROFILE", "description": "build_profile() 的返回值被赋给变量 user_profile", "weight": 9.0, "combined_degree": 9}, {"id": "8b7c31b3-11e7-4323-99c2-08cc9c58b860", "source": "BUILD_PROFILE()", "target": "FIRST", "from": "BUILD_PROFILE()", "to": "FIRST", "description": "first 是 build_profile() 的一个位置参数", "weight": 8.0, "combined_degree": 8}, {"id": "934e7a22-60cb-4582-b97f-7e2bb40ed5fe", "source": "BUILD_PROFILE()", "target": "LAST", "from": "BUILD_PROFILE()", "to": "LAST", "description": "last 是 build_profile() 的一个位置参数", "weight": 8.0, "combined_degree": 8}, {"id": "92dbfbea-e4d8-44b9-b8f0-7c85911e9d36", "source": "**USER_INFO", "target": "字典", "from": "**USER_INFO", "to": "字典", "description": "**user_info 会被自动转换为一个字典对象", "weight": 10.0, "combined_degree": 12}, {"id": "a6d37e13-a5a6-40b9-b115-aa1ef8a4b22a", "source": "字典", "target": "USER_PROFILE", "from": "字典", "to": "USER_PROFILE", "description": "user_profile 是一个字典，包含用户的所有信息", "weight": 1.0, "combined_degree": 12}, {"id": "2f542aff-1fe3-4c66-bb6d-9f6368d891a0", "source": "**KWARGS", "target": "关键字实参", "from": "**KWARGS", "to": "关键字实参", "description": "**kwargs用于收集任意数量的关键字实参", "weight": 10.0, "combined_degree": 11}, {"id": "6ff87b04-ac80-4eb5-b84e-f03f41287743", "source": "**KWARGS", "target": "BUILD_PROFILE()", "from": "**KWARGS", "to": "BUILD_PROFILE()", "description": "build_profile()函数使用**kwargs来接收任意数量的关键字实参", "weight": 9.0, "combined_degree": 9}, {"id": "b9bc9591-54c1-4698-b2fb-357dea67953e", "source": "实参", "target": "函数", "from": "实参", "to": "函数", "description": "函数通过实参接收外部传入的数据或信息，以完成其任务。实参是调用函数时提供的具体值，用于为函数的执行提供必要的输入，从而使函数能够根据这些外部信息进行相应的处理。", "weight": 20.0, "combined_degree": 40}, {"id": "2b211096-238e-4116-8e9f-f6ab444725a0", "source": "形参", "target": "函数", "from": "形参", "to": "函数", "description": "函数通过形参定义接收哪些数据", "weight": 1.0, "combined_degree": 39}, {"id": "15fcd762-b81f-48ee-9344-d23afebe1e19", "source": "BUILD_PROFILE()", "target": "函数", "from": "BUILD_PROFILE()", "to": "函数", "description": "build_profile()是一个函数的实例", "weight": 10.0, "combined_degree": 37}, {"id": "6b21fb96-d2a8-4cbe-91c7-0cf2c41ed3e3", "source": "MAKE_CAR", "target": "字典", "from": "MAKE_CAR", "to": "字典", "description": "make_car函数返回一个包含汽车信息的字典", "weight": 9.0, "combined_degree": 12}, {"id": "a19cd00b-8f35-4b3f-8f37-1704cc2a75d4", "source": "MAKE_CAR", "target": "关键字实参", "from": "MAKE_CAR", "to": "关键字实参", "description": "make_car函数使用关键字实参来接收可选的汽车信息", "weight": 9.0, "combined_degree": 11}, {"id": "23b8746c-0650-4374-9ceb-1a760ee22ee8", "source": "模块", "target": "函数", "from": "模块", "to": "函数", "description": "函数可以被存储在模块中以实现代码复用和逻辑分离", "weight": 8.0, "combined_degree": 35}, {"id": "a0d53258-5ec2-4677-8200-7c505cd86e81", "source": "模块", "target": "IMPORT", "from": "模块", "to": "IMPORT", "description": "import关键字用于导入模块，使得模块中的函数可以在当前程序中使用", "weight": 10.0, "combined_degree": 13}, {"id": "eb1f3a32-f218-4c21-8bb0-2e18bdbb6d82", "source": "IMPORT", "target": "函数", "from": "IMPORT", "to": "函数", "description": "使用import关键字可以导入其他模块中的函数", "weight": 1.0, "combined_degree": 38}, {"id": "691a1883-d839-4d3c-8b84-bf29669fadfd", "source": "MAKE_PIZZA()", "target": "PIZZA", "from": "MAKE_PIZZA()", "to": "PIZZA", "description": "make_pizza() 是定义在 pizza 模块中的一个函数。", "weight": 19.0, "combined_degree": 10}, {"id": "47f3b421-e38d-4ce3-b601-6ec7378a0726", "source": "MAKE_PIZZA()", "target": "MAKING_PIZZAS", "from": "MAKE_PIZZA()", "to": "MAKING_PIZZAS", "description": "making_pizzas模块调用了pizza模块中的make_pizza()函数", "weight": 10.0, "combined_degree": 8}, {"id": "570c3a06-732a-4fc1-bb85-7a346b732823", "source": "MAKE_PIZZA()", "target": "函数调用", "from": "MAKE_PIZZA()", "to": "函数调用", "description": "make_pizza()函数通过函数调用语法结构被执行", "weight": 1.0, "combined_degree": 17}, {"id": "c514ce72-61f0-432b-89e6-225c96d4dbc7", "source": "IMPORT", "target": "PIZZA", "from": "IMPORT", "to": "PIZZA", "description": "IMPORT 是 Python 中的一个关键字，用于导入模块。在“中文Python编程教程”社区中，IMPORT 常用于导入名为 PIZZA 的模块。通过使用 import 语句，用户可以在程序中访问 PIZZA 模块中定义的函数、类或变量，从而实现模块化编程。这种用法体现了 Python 编程中常见的模块导入机制，有助于代码的组织与重用。", "weight": 18.0, "combined_degree": 12}, {"id": "4f7dd4ad-353e-411c-bb22-2a8d023951cc", "source": "PIZZA", "target": "MAKING_PIZZAS", "from": "PIZZA", "to": "MAKING_PIZZAS", "description": "making_pizzas模块通过import语句导入pizza模块", "weight": 9.0, "combined_degree": 6}, {"id": "4927888a-8ffd-4a9f-ab32-cddfdc41db19", "source": "IMPORT", "target": "MODULE_NAME.FUNCTION_NAME", "from": "IMPORT", "to": "MODULE_NAME.FUNCTION_NAME", "description": "使用 import 关键字导入整个模块后，可通过 module_name.function_name 语法调用函数", "weight": 9.0, "combined_degree": 9}, {"id": "8b854510-c0a3-46e6-b922-81d518185105", "source": "FROM", "target": "FROM MODULE_NAME IMPORT FUNCTION_NAME", "from": "FROM", "to": "FROM MODULE_NAME IMPORT FUNCTION_NAME", "description": "from 关键字用于构建从模块中导入特定函数的语法结构", "weight": 10.0, "combined_degree": 6}, {"id": "a2e2b65f-9c42-4d2a-8b4d-fcbfaffc267e", "source": "FROM", "target": "FROM MODULE_NAME IMPORT FUNCTION_0 , FUNCTION_1 , FUNCTION_2", "from": "FROM", "to": "FROM MODULE_NAME IMPORT FUNCTION_0 , FUNCTION_1 , FUNCTION_2", "description": "from 关键字也用于从模块中导入多个函数", "weight": 10.0, "combined_degree": 5}, {"id": "0a6ac190-a1db-4c45-b2f6-55c4c07e53a0", "source": "AS", "target": "AS 别名", "from": "AS", "to": "AS 别名", "description": "as 关键字用于构建给函数或模块指定别名的语法结构", "weight": 10.0, "combined_degree": 6}, {"id": "b72c6374-fad3-4dff-9f9e-c22978109fbe", "source": "FROM MODULE_NAME IMPORT FUNCTION_NAME", "target": "MAKE_PIZZA", "from": "FROM MODULE_NAME IMPORT FUNCTION_NAME", "to": "MAKE_PIZZA", "description": "make_pizza 函数可以通过 from module_name import function_name 语法导入", "weight": 9.0, "combined_degree": 10}, {"id": "e71f66f6-a909-4a27-83b3-89e6690214df", "source": "MAKE_PIZZA", "target": "AS 别名", "from": "MAKE_PIZZA", "to": "AS 别名", "description": "make_pizza 函数可以使用 as 关键字指定别名以简化调用", "weight": 1.0, "combined_degree": 10}, {"id": "9ff938ba-ebee-4298-a81b-8d1b6ca5402d", "source": "MAKE_PIZZA()", "target": "MP()", "from": "MAKE_PIZZA()", "to": "MP()", "description": "mp() 是 make_pizza() 函数的别名，通过 as 关键字指定", "weight": 10.0, "combined_degree": 8}, {"id": "b2a7e62b-3408-49de-ac79-c70afa665a76", "source": "MAKE_PIZZA()", "target": "IMPORT", "from": "MAKE_PIZZA()", "to": "IMPORT", "description": "import 语句用于从模块中导入 make_pizza() 函数", "weight": 8.0, "combined_degree": 14}, {"id": "fcb8b8fb-cbde-4881-a8a5-ba439e647157", "source": "MP()", "target": "AS", "from": "MP()", "to": "AS", "description": "as 关键字用于将 make_pizza() 重命名为 mp()", "weight": 10.0, "combined_degree": 6}, {"id": "618fe5e5-171e-435d-9797-15ea3fd0e737", "source": "AS", "target": "IMPORT", "from": "AS", "to": "IMPORT", "description": "import 语法结构中使用 as 关键字为函数或模块指定别名", "weight": 9.0, "combined_degree": 12}, {"id": "cec94f4a-2372-4d19-80ea-7bf5f5f6c98b", "source": "AS", "target": "PIZZA", "from": "AS", "to": "PIZZA", "description": "as 关键字可用于为 pizza 模块指定别名", "weight": 1.0, "combined_degree": 8}, {"id": "5c5badd2-015a-4549-82ce-8881bb7602ed", "source": "IMPORT", "target": "模块", "from": "IMPORT", "to": "模块", "description": "“IMPORT”是Python中的一个关键字，用于导入“模块”。通过使用import关键字，程序可以加载指定的模块，从而在代码中访问和使用该模块中定义的函数、类和变量等内容。这一机制是Python模块化编程的基础，有助于代码的组织与重用。", "weight": 20.0, "combined_degree": 13}, {"id": "f7eaabb8-cb04-4d43-ace3-3faced2e382e", "source": "IMPORT", "target": "AS", "from": "IMPORT", "to": "AS", "description": "as关键字常与import一起使用，用于为导入的模块指定别名", "weight": 9.0, "combined_degree": 12}, {"id": "a4714123-d452-4b22-b28d-207d3d51bf59", "source": "IMPORT", "target": "*", "from": "IMPORT", "to": "*", "description": "在Python编程中，*运算符在import语句中用于导入模块中的所有对象，包括函数、变量等。这种用法允许用户通过from module import *的形式一次性引入模块中定义的所有公共成员，从而简化代码书写。需要注意的是，虽然这种方式在某些教学示例中常见，但在实际开发中通常不推荐使用，因为它可能导致命名空间污染和代码可读性降低。", "weight": 18.0, "combined_degree": 9}, {"id": "c4b0416a-0454-45db-ae37-3a62559657f5", "source": "点号", "target": "模块", "from": "点号", "to": "模块", "description": "点号用于访问模块中的函数或变量，是模块调用的语法结构", "weight": 8.0, "combined_degree": 7}, {"id": "434a0f22-6adc-47fd-b899-4cb53c620d5d", "source": "点号", "target": "MAKE_PIZZA", "from": "点号", "to": "MAKE_PIZZA", "description": "make_pizza函数通常通过点号语法从模块中调用", "weight": 1.0, "combined_degree": 10}, {"id": "113bfd8d-3d36-4047-91bf-98e1e2b698be", "source": "FROM", "target": "模块", "from": "FROM", "to": "模块", "description": "from关键字用于从模块中导入特定内容", "weight": 10.0, "combined_degree": 9}, {"id": "adb70cfc-6c11-4f94-8b6c-b3aaedf7c396", "source": "MAKE_PIZZA", "target": "模块", "from": "MAKE_PIZZA", "to": "模块", "description": "make_pizza函数定义在模块中，通过导入模块可以使用该函数", "weight": 8.0, "combined_degree": 13}, {"id": "a812540e-c228-4b5b-9328-a60df5645c21", "source": "IMPORT", "target": "FROM", "from": "IMPORT", "to": "FROM", "description": "from关键字与import语句结合使用以导入模块中的特定对象", "weight": 10.0, "combined_degree": 12}, {"id": "aa5f8b8b-b34e-4f4e-8611-99dd2df66c64", "source": "函数", "target": "文档字符串", "from": "函数", "to": "文档字符串", "description": "文档字符串用于描述函数的功能，紧跟在函数定义之后", "weight": 10.0, "combined_degree": 33}, {"id": "b9796481-4bdc-41ac-b4dc-e2ddc8a25647", "source": "函数", "target": "关键字实参", "from": "函数", "to": "关键字实参", "description": "函数在调用时可以通过关键字实参接收输入值，这种方式不仅使参数的传递更加灵活，还能显著提高代码的可读性。关键字实参允许调用者在调用函数时显式指定参数名称，从而避免位置错误，并使函数调用的意图更加清晰。", "weight": 18.0, "combined_degree": 39}, {"id": "72cd885d-4f40-47c0-8e8f-57431491fae0", "source": "形参", "target": "默认值", "from": "形参", "to": "默认值", "description": "形参可以指定默认值以在未提供实参时使用", "weight": 9.0, "combined_degree": 13}, {"id": "b165c402-c9d9-45b4-98c2-b3a86fa87895", "source": "PEP 8", "target": "代码行长度限制", "from": "PEP 8", "to": "代码行长度限制", "description": "PEP 8建议每行代码不超过79个字符以提高可读性", "weight": 1.0, "combined_degree": 2}, {"id": "5a822500-3020-40e5-a657-082434e1c1bc", "source": "DEF", "target": "函数", "from": "DEF", "to": "函数", "description": "def关键字用于定义函数", "weight": 10.0, "combined_degree": 35}, {"id": "cc430594-ca21-4dfa-8954-2ce82b82872a", "source": "函数", "target": "位置实参", "from": "函数", "to": "位置实参", "description": "函数可以通过位置实参接收输入值", "weight": 9.0, "combined_degree": 38}, {"id": "6becd381-787c-4dad-9f60-962d67e26acc", "source": "函数", "target": "任意数量实参", "from": "函数", "to": "任意数量实参", "description": "函数可以通过*args或**kwargs接收任意数量的实参", "weight": 9.0, "combined_degree": 31}, {"id": "0d326044-cb63-41af-b59d-4df82e2bead1", "source": "函数", "target": "函数编写指南", "from": "函数", "to": "函数编写指南", "description": "函数编写指南为开发者提供了编写函数的最佳实践，旨在帮助他们创建结构良好、易读的函数。这些指南在中文Python编程教程社区中被广泛引用，有助于提升代码质量和可维护性。", "weight": 2.0, "combined_degree": 31}, {"id": "36c518e3-79c4-43d2-a001-3b9a080f2c08", "source": "位置实参", "target": "函数", "from": "位置实参", "to": "函数", "description": "函数可以通过位置实参接收参数", "weight": 9.0, "combined_degree": 38}, {"id": "bcc0c2a6-dc15-44b4-8568-190c70d7a89d", "source": "关键字实参", "target": "函数", "from": "关键字实参", "to": "函数", "description": "函数可以通过关键字实参接收参数", "weight": 9.0, "combined_degree": 39}, {"id": "705ee5d1-6345-475b-b1a2-08a8c89e43dd", "source": "任意数量的实参", "target": "函数", "from": "任意数量的实参", "to": "函数", "description": "函数可以通过*args或**kwargs接收任意数量的实参", "weight": 9.0, "combined_degree": 31}, {"id": "34603df7-91a2-4466-9118-a7e53ea59848", "source": "函数", "target": "返回值", "from": "函数", "to": "返回值", "description": "函数可以返回值以供调用者使用", "weight": 10.0, "combined_degree": 32}, {"id": "0a3665f2-e5d2-4e23-a4d8-d95fcafcabe5", "source": "函数", "target": "字典", "from": "函数", "to": "字典", "description": "函数可以操作字典作为输入或输出", "weight": 8.0, "combined_degree": 40}, {"id": "ced6fca6-badc-43b6-b25e-01924b1a3016", "source": "函数", "target": "IF", "from": "函数", "to": "IF", "description": "函数体中可以包含if语句以实现条件逻辑", "weight": 8.0, "combined_degree": 36}, {"id": "b9829cad-5650-4289-aa8c-61d2f9c3e613", "source": "函数", "target": "WHILE", "from": "函数", "to": "WHILE", "description": "函数体中可以包含while语句以实现循环逻辑", "weight": 8.0, "combined_degree": 36}, {"id": "8fa0bd89-20ef-465a-bca3-6f5b4be2a71b", "source": "函数", "target": "函数调用", "from": "函数", "to": "函数调用", "description": "函数调用用于执行函数中定义的代码", "weight": 10.0, "combined_degree": 41}, {"id": "21fec760-960e-43ef-bf21-b874c9879ab9", "source": "函数", "target": "函数名", "from": "函数", "to": "函数名", "description": "函数名用于标识和调用函数，应具有描述性", "weight": 9.0, "combined_degree": 32}, {"id": "861a1f2a-1d2e-4ebc-9b33-156f0b8ccdb2", "source": "函数调用", "target": "函数名", "from": "函数调用", "to": "函数名", "description": "函数调用通过函数名引用函数", "weight": 9.0, "combined_degree": 13}, {"id": "ff692408-9bfa-4d4e-b180-2ab4c9501fee", "source": "函数", "target": "类", "from": "函数", "to": "类", "description": "类将函数封装在一起，使得函数可以作为类的方法被调用", "weight": 8.0, "combined_degree": 31}, {"id": "0d2d1785-75f5-425a-b75b-bfedbc014a96", "source": "函数", "target": "函数", "from": "函数", "to": "函数", "description": "程序可以通过调用多个函数来完成复杂任务，每个函数负责一项具体工作", "weight": 1.0, "combined_degree": 60}], "metadata": {"total_nodes": 113, "total_edges": 220, "source": "GraphRAG"}}