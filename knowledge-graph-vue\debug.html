<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>知识图谱调试版本</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script src="https://pixijs.download/release/pixi.min.js"></script>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f0f23 100%);
        color: #ffffff;
        height: 100vh;
      }

      .container {
        display: flex;
        height: 100vh;
      }

      .sidebar {
        width: 300px;
        background: rgba(0, 0, 0, 0.9);
        border-right: 1px solid rgba(78, 205, 196, 0.3);
        padding: 20px;
        overflow-y: auto;
      }

      .sidebar h1 {
        color: #4ecdc4;
        margin-bottom: 20px;
        font-size: 18px;
      }

      .debug-info {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(78, 205, 196, 0.2);
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 16px;
        font-family: 'Courier New', monospace;
        font-size: 12px;
      }

      .debug-info h3 {
        color: #4ecdc4;
        margin-bottom: 8px;
      }

      .debug-info pre {
        color: rgba(255, 255, 255, 0.8);
        white-space: pre-wrap;
        word-break: break-word;
      }

      .canvas-area {
        flex: 1;
        position: relative;
        background: #0a0a0a;
      }

      .status {
        position: absolute;
        top: 20px;
        left: 20px;
        background: rgba(0, 0, 0, 0.8);
        padding: 10px;
        border-radius: 6px;
        border: 1px solid rgba(78, 205, 196, 0.3);
        font-size: 12px;
      }

      .loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        color: #4ecdc4;
      }

      .loading-spinner {
        border: 3px solid rgba(78, 205, 196, 0.3);
        border-top: 3px solid #4ecdc4;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .error {
        color: #ff6b6b;
      }

      .success {
        color: #4ecdc4;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div class="container">
        <div class="sidebar">
          <h1>🔍 知识图谱调试</h1>

          <div class="debug-info">
            <h3>数据加载状态</h3>
            <pre>{{ debugInfo.dataStatus }}</pre>
          </div>

          <div class="debug-info">
            <h3>数据统计</h3>
            <pre>{{ debugInfo.dataStats }}</pre>
          </div>

          <div class="debug-info">
            <h3>渲染状态</h3>
            <pre>{{ debugInfo.renderStatus }}</pre>
          </div>

          <div class="debug-info">
            <h3>错误信息</h3>
            <pre class="error">{{ debugInfo.errors }}</pre>
          </div>

          <div class="debug-info">
            <h3>控制台日志</h3>
            <pre>{{ debugInfo.logs }}</pre>
          </div>
        </div>

        <div class="canvas-area">
          <div class="status">状态: {{ status }}</div>

          <div v-if="loading" class="loading">
            <div class="loading-spinner"></div>
            <h3>正在加载和初始化...</h3>
          </div>

          <div v-else-if="error" class="loading">
            <h3 class="error">❌ 错误</h3>
            <p>{{ error }}</p>
          </div>

          <div v-else ref="canvasContainer" style="width: 100%; height: 100%"></div>
        </div>
      </div>
    </div>

    <script>
      const { createApp, ref, onMounted, reactive } = Vue

      createApp({
        setup() {
          const loading = ref(true)
          const error = ref(null)
          const status = ref('初始化中...')
          const canvasContainer = ref(null)

          const debugInfo = reactive({
            dataStatus: '等待加载...',
            dataStats: '无数据',
            renderStatus: '未开始',
            errors: '无错误',
            logs: '',
          })

          let app = null
          let simulation = null
          let graphData = null

          function log(message) {
            console.log(message)
            debugInfo.logs += new Date().toLocaleTimeString() + ': ' + message + '\n'
          }

          async function loadData() {
            try {
              log('开始加载数据...')
              debugInfo.dataStatus = '正在加载...'

              const response = await fetch('./public/graph_data.json')
              if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`)
              }

              graphData = await response.json()

              debugInfo.dataStatus = '✅ 加载成功'
              debugInfo.dataStats = `节点: ${graphData.nodes?.length || 0}\n边: ${graphData.edges?.length || 0}`

              log(
                `数据加载成功: ${graphData.nodes?.length || 0} 节点, ${graphData.edges?.length || 0} 边`,
              )

              if (graphData.nodes && graphData.nodes.length > 0) {
                log('样本节点: ' + JSON.stringify(graphData.nodes[0], null, 2))
              }

              return true
            } catch (err) {
              debugInfo.dataStatus = '❌ 加载失败'
              debugInfo.errors = err.message
              log('数据加载失败: ' + err.message)
              error.value = err.message
              return false
            }
          }

          async function initPixi() {
            try {
              log('开始初始化 Pixi.js...')
              debugInfo.renderStatus = '初始化 Pixi.js...'

              // 等待 DOM 更新
              await Vue.nextTick()
              await new Promise((resolve) => setTimeout(resolve, 100))

              if (!canvasContainer.value) {
                throw new Error('Canvas 容器未找到')
              }

              // 检查容器是否在 DOM 中
              if (!document.contains(canvasContainer.value)) {
                throw new Error('Canvas 容器不在 DOM 中')
              }

              log('Canvas 容器检查通过')

              const containerRect = canvasContainer.value.getBoundingClientRect()
              const width = Math.max(containerRect.width, canvasContainer.value.clientWidth, 800)
              const height = Math.max(containerRect.height, canvasContainer.value.clientHeight, 600)

              log(`创建 Pixi 应用: ${width}x${height}`)
              log(
                `容器信息: clientWidth=${canvasContainer.value.clientWidth}, clientHeight=${canvasContainer.value.clientHeight}`,
              )

              app = new PIXI.Application({
                width: width,
                height: height,
                backgroundColor: 0x0a0a0a,
                antialias: true,
              })

              log('Pixi.js 应用创建成功')

              // 兼容新旧版本
              const canvas = app.canvas || app.view
              if (!canvas) {
                throw new Error('无法获取 Pixi.js 画布')
              }

              log('获取到画布，准备添加到容器')
              canvasContainer.value.appendChild(canvas)
              log('画布已添加到容器')

              debugInfo.renderStatus = '✅ Pixi.js 初始化成功'
              log('Pixi.js 初始化成功')

              return true
            } catch (err) {
              debugInfo.renderStatus = '❌ Pixi.js 初始化失败'
              debugInfo.errors += '\nPixi.js: ' + err.message
              log('Pixi.js 初始化失败: ' + err.message)
              console.error('Pixi.js 详细错误:', err)
              return false
            }
          }

          async function initD3() {
            try {
              log('开始初始化 D3.js...')

              const nodes = graphData.nodes || []
              const edges = graphData.edges || []

              log(`D3 数据: ${nodes.length} 节点, ${edges.length} 边`)

              simulation = d3
                .forceSimulation(nodes)
                .force(
                  'link',
                  d3
                    .forceLink(edges)
                    .id((d) => d.id)
                    .distance(50),
                )
                .force('charge', d3.forceManyBody().strength(-300))
                .force('center', d3.forceCenter(app.screen.width / 2, app.screen.height / 2))
                .force(
                  'collision',
                  d3.forceCollide().radius((d) => Math.sqrt(d.degree || 1) * 2 + 5),
                )
                .on('tick', updatePositions)

              log('D3.js 力导向模拟初始化成功')
              return true
            } catch (err) {
              debugInfo.errors += '\nD3.js: ' + err.message
              log('D3.js 初始化失败: ' + err.message)
              return false
            }
          }

          function createGraphics() {
            try {
              log('开始创建图形...')

              const nodes = graphData.nodes || []
              const edges = graphData.edges || []

              // 创建边
              edges.forEach((edge, index) => {
                const line = new PIXI.Graphics()
                app.stage.addChild(line)
                if (index < 5) log(`创建边 ${index}: ${edge.source} -> ${edge.target}`)
              })

              // 创建节点
              nodes.forEach((node, index) => {
                const circle = new PIXI.Graphics()
                const radius = Math.sqrt(node.degree || 1) * 2 + 5
                const color = getNodeColor(node.degree || 0)

                circle.beginFill(color, 0.8)
                circle.drawCircle(0, 0, radius)
                circle.endFill()

                circle.interactive = true
                circle.buttonMode = true
                circle.on('pointerdown', () => log(`点击节点: ${node.title || node.id}`))

                app.stage.addChild(circle)

                if (index < 5)
                  log(`创建节点 ${index}: ${node.title || node.id} (度数: ${node.degree || 0})`)
              })

              debugInfo.renderStatus += `\n✅ 图形创建完成: ${nodes.length} 节点, ${edges.length} 边`
              log(`图形创建完成: ${nodes.length} 节点, ${edges.length} 边`)
            } catch (err) {
              debugInfo.errors += '\n图形创建: ' + err.message
              log('图形创建失败: ' + err.message)
            }
          }

          function getNodeColor(degree) {
            if (degree > 20) return 0xff6b6b
            if (degree > 10) return 0x4ecdc4
            if (degree > 5) return 0x45b7d1
            if (degree > 2) return 0x96ceb4
            if (degree > 0) return 0xffeaa7
            return 0xdda0dd
          }

          function updatePositions() {
            // 更新位置的逻辑
          }

          async function initialize() {
            try {
              status.value = '加载数据...'
              const dataLoaded = await loadData()
              if (!dataLoaded) return

              status.value = '初始化渲染...'
              await Vue.nextTick()

              const pixiSuccess = await initPixi()
              if (!pixiSuccess) return

              const d3Success = await initD3()
              if (!d3Success) return

              status.value = '创建图形...'
              createGraphics()

              status.value = '✅ 初始化完成'
              loading.value = false

              log('所有初始化完成！')
            } catch (err) {
              error.value = err.message
              debugInfo.errors += '\n总体错误: ' + err.message
              log('初始化失败: ' + err.message)
              loading.value = false
            }
          }

          onMounted(() => {
            log('组件挂载，开始初始化...')
            initialize()
          })

          return {
            loading,
            error,
            status,
            canvasContainer,
            debugInfo,
          }
        },
      }).mount('#app')
    </script>
  </body>
</html>
