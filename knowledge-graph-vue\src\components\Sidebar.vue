<template>
  <div class="sidebar" :class="{ collapsed }" :style="{ width: width + 'px' }">
    <!-- 头部区域 -->
    <div class="sidebar-header">
      <div v-if="!collapsed" class="header-content">
        <h2 class="title">🚀 D3 + Pixi.js 知识图谱</h2>
        <button class="collapse-btn" @click="$emit('toggle')" title="收起侧边栏">←</button>
      </div>
      <div v-else class="header-collapsed">
        <button class="expand-btn" @click="$emit('toggle')" title="展开侧边栏">→</button>
      </div>
    </div>

    <!-- 主要内容 -->
    <div v-if="!collapsed" class="sidebar-content">
      <!-- 统计信息 -->
      <StatsDisplay />

      <!-- 物理引擎控制 -->
      <!-- <PhysicsControls /> -->

      <!-- 性能设置 -->
      <!-- <PerformanceSettings /> -->

      <!-- 节点度数图例 -->
      <!-- <DegreeLegend /> -->

      <!-- 筛选控件 -->
      <FilterControls />
    </div>
  </div>
</template>

<script setup>
import StatsDisplay from './StatsDisplay.vue'
// import PhysicsControls from './PhysicsControls.vue'
// import PerformanceSettings from './PerformanceSettings.vue'
// import DegreeLegend from './DegreeLegend.vue'
import FilterControls from './FilterControls.vue'

// Props
defineProps({
  collapsed: {
    type: Boolean,
    default: false,
  },
  width: {
    type: Number,
    default: 400,
  },
})

// Emits
defineEmits(['toggle'])
</script>

<style scoped>
.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  background: rgba(0, 0, 0, 0.9);
  border-right: 1px solid rgba(78, 205, 196, 0.3);
  backdrop-filter: blur(10px);
  transition: width 0.3s ease;
  z-index: 100;
  overflow: hidden;
}

.sidebar.collapsed {
  width: 60px !important;
}

.sidebar-header {
  height: 60px;
  display: flex;
  align-items: center;
  padding: 0 16px;
  border-bottom: 1px solid rgba(78, 205, 196, 0.2);
  background: rgba(78, 205, 196, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #4ecdc4;
  margin: 0;
}

.collapse-btn,
.expand-btn {
  background: none;
  border: none;
  color: #4ecdc4;
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.collapse-btn:hover,
.expand-btn:hover {
  background: rgba(78, 205, 196, 0.2);
}

.header-collapsed {
  display: flex;
  justify-content: center;
  width: 100%;
}

.sidebar-content {
  height: calc(100vh - 60px);
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 自定义滚动条 */
.sidebar-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: rgba(78, 205, 196, 0.5);
  border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
  background: rgba(78, 205, 196, 0.7);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .title {
    font-size: 16px;
  }

  .sidebar-content {
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
  }

  .sidebar:not(.collapsed) {
    transform: translateX(0);
    width: 85vw !important;
    max-width: 400px;
  }

  .sidebar.collapsed {
    width: 50px !important;
    transform: translateX(0);
  }

  .title {
    font-size: 14px;
  }

  .sidebar-content {
    padding: 12px;
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .sidebar:not(.collapsed) {
    width: 100vw !important;
  }

  .sidebar.collapsed {
    width: 45px !important;
  }

  .title {
    font-size: 12px;
  }

  .sidebar-header {
    height: 50px;
    padding: 0 12px;
  }

  .sidebar-content {
    height: calc(100vh - 50px);
    padding: 8px;
    gap: 12px;
  }

  .collapse-btn,
  .expand-btn {
    font-size: 16px;
    padding: 6px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .collapse-btn,
  .expand-btn {
    min-height: 44px;
    min-width: 44px;
  }

  .sidebar-content {
    /* 增加触摸目标大小 */
    --touch-target-size: 44px;
  }
}

/* 横屏模式优化 */
@media (orientation: landscape) and (max-height: 500px) {
  .sidebar-header {
    height: 40px;
  }

  .sidebar-content {
    height: calc(100vh - 40px);
    padding: 8px;
    gap: 8px;
  }

  .title {
    font-size: 12px;
  }
}
</style>
