<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 D3 + Pixi.js 知识图谱</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script src="https://pixijs.download/release/pixi.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f0f23 100%);
            color: #ffffff;
            overflow: hidden;
            height: 100vh;
        }

        .container {
            display: flex;
            height: 100vh;
        }

        .sidebar {
            width: 400px;
            background: rgba(0, 0, 0, 0.9);
            border-right: 1px solid rgba(78, 205, 196, 0.3);
            backdrop-filter: blur(10px);
            padding: 20px;
            overflow-y: auto;
        }

        .sidebar h1 {
            color: #4ecdc4;
            margin-bottom: 20px;
            font-size: 20px;
        }

        .stats {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(78, 205, 196, 0.2);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 20px;
        }

        .stats h3 {
            color: #4ecdc4;
            margin-bottom: 12px;
            font-size: 16px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 12px;
            text-align: center;
        }

        .stat-value {
            font-size: 20px;
            font-weight: 700;
            color: #4ecdc4;
            margin-bottom: 4px;
            font-family: 'Courier New', monospace;
        }

        .stat-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }

        .controls {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(78, 205, 196, 0.2);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 20px;
        }

        .controls h3 {
            color: #4ecdc4;
            margin-bottom: 12px;
            font-size: 16px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            margin: 4px;
            background: #4ecdc4;
            color: #0a0a0a;
        }

        .btn:hover {
            background: #45b7d1;
            transform: translateY(-1px);
        }

        .canvas-area {
            flex: 1;
            position: relative;
        }

        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #4ecdc4;
        }

        .loading-spinner {
            border: 3px solid rgba(78, 205, 196, 0.3);
            border-top: 3px solid #4ecdc4;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .performance-panel {
            position: absolute;
            top: 16px;
            right: 16px;
            background: rgba(0, 0, 0, 0.9);
            border: 1px solid rgba(78, 205, 196, 0.3);
            border-radius: 8px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .performance-panel .title {
            color: #4ecdc4;
            margin-bottom: 8px;
            font-weight: bold;
        }

        .performance-panel .metric {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;
        }

        .performance-panel .metric .value {
            color: #ffffff;
            font-weight: bold;
        }

        .legend {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(78, 205, 196, 0.2);
            border-radius: 12px;
            padding: 16px;
        }

        .legend h3 {
            color: #4ecdc4;
            margin-bottom: 12px;
            font-size: 16px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 12px;
        }

        .legend-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .legend-text {
            color: rgba(255, 255, 255, 0.8);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <h1>🚀 D3 + Pixi.js 知识图谱</h1>
            
            <div class="stats">
                <h3>📊 实时统计</h3>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value" id="totalNodes">0</div>
                        <div class="stat-label">总节点数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="totalEdges">0</div>
                        <div class="stat-label">总边数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="visibleNodes">0</div>
                        <div class="stat-label">可见节点</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="visibleEdges">0</div>
                        <div class="stat-label">可见边数</div>
                    </div>
                </div>
            </div>

            <div class="controls">
                <h3>⚡ 物理引擎控制</h3>
                <button class="btn" onclick="togglePhysics()">切换物理引擎</button>
                <button class="btn" onclick="restartLayout()">重新布局</button>
                <button class="btn" onclick="resetView()">重置视图</button>
            </div>

            <div class="legend">
                <h3>⭐ 节点度数图例</h3>
                <div class="legend-item">
                    <div class="legend-dot" style="background-color: #FF6B6B;"></div>
                    <span class="legend-text">超级节点 (>20)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-dot" style="background-color: #4ECDC4;"></div>
                    <span class="legend-text">重要节点 (11-20)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-dot" style="background-color: #45B7D1;"></div>
                    <span class="legend-text">活跃节点 (6-10)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-dot" style="background-color: #96CEB4;"></div>
                    <span class="legend-text">普通节点 (3-5)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-dot" style="background-color: #FFEAA7;"></div>
                    <span class="legend-text">边缘节点 (1-2)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-dot" style="background-color: #DDA0DD;"></div>
                    <span class="legend-text">孤立节点 (0)</span>
                </div>
            </div>
        </div>

        <div class="canvas-area">
            <div class="loading" id="loading">
                <div class="loading-spinner"></div>
                <h2>正在初始化 GPU 渲染...</h2>
                <p>加载知识图谱数据</p>
            </div>

            <div class="performance-panel" id="performancePanel" style="display: none;">
                <div class="title">性能监控</div>
                <div class="metric">
                    <span>FPS:</span>
                    <span class="value" id="fps">60</span>
                </div>
                <div class="metric">
                    <span>节点:</span>
                    <span class="value" id="nodeCount">0</span>
                </div>
                <div class="metric">
                    <span>边:</span>
                    <span class="value" id="edgeCount">0</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let app, simulation, graphData;
        let nodes = [], edges = [];
        let nodeGraphics = new Map();
        let edgeGraphics = new Map();
        let physicsRunning = true;

        // 初始化应用
        async function initApp() {
            try {
                // 加载数据
                const response = await fetch('./public/graph_data.json');
                graphData = await response.json();
                
                nodes = graphData.nodes || [];
                edges = graphData.edges || [];
                
                console.log('数据加载成功:', { nodes: nodes.length, edges: edges.length });
                
                // 更新统计
                updateStats();
                
                // 初始化 Pixi.js
                initPixi();
                
                // 初始化 D3 力导向
                initD3();
                
                // 创建图形
                createGraphics();
                
                // 隐藏加载界面
                document.getElementById('loading').style.display = 'none';
                document.getElementById('performancePanel').style.display = 'block';
                
            } catch (error) {
                console.error('初始化失败:', error);
                document.getElementById('loading').innerHTML = `
                    <h2>❌ 加载失败</h2>
                    <p>${error.message}</p>
                `;
            }
        }

        // 初始化 Pixi.js
        function initPixi() {
            const canvasArea = document.querySelector('.canvas-area');
            
            app = new PIXI.Application({
                width: canvasArea.clientWidth,
                height: canvasArea.clientHeight,
                backgroundColor: 0x0a0a0a,
                antialias: true
            });
            
            canvasArea.appendChild(app.view);
        }

        // 初始化 D3 力导向
        function initD3() {
            simulation = d3.forceSimulation(nodes)
                .force('link', d3.forceLink(edges).id(d => d.id).distance(50))
                .force('charge', d3.forceManyBody().strength(-300))
                .force('center', d3.forceCenter(app.screen.width / 2, app.screen.height / 2))
                .force('collision', d3.forceCollide().radius(d => Math.sqrt(d.degree) * 2 + 5))
                .on('tick', updatePositions);
        }

        // 创建图形
        function createGraphics() {
            // 创建边
            edges.forEach(edge => {
                const line = new PIXI.Graphics();
                edgeGraphics.set(edge.id, line);
                app.stage.addChild(line);
            });

            // 创建节点
            nodes.forEach(node => {
                const circle = new PIXI.Graphics();
                const radius = Math.sqrt(node.degree) * 2 + 5;
                const color = getNodeColor(node.degree);
                
                circle.beginFill(color, 0.8);
                circle.drawCircle(0, 0, radius);
                circle.endFill();
                
                // 重要节点添加边框
                if (node.degree > 10) {
                    circle.lineStyle(2, 0xffffff, 0.8);
                    circle.drawCircle(0, 0, radius);
                }
                
                circle.interactive = true;
                circle.buttonMode = true;
                circle.on('pointerdown', () => console.log('节点点击:', node.title));
                
                nodeGraphics.set(node.id, circle);
                app.stage.addChild(circle);
            });
        }

        // 获取节点颜色
        function getNodeColor(degree) {
            if (degree > 20) return 0xFF6B6B;
            if (degree > 10) return 0x4ECDC4;
            if (degree > 5) return 0x45B7D1;
            if (degree > 2) return 0x96CEB4;
            if (degree > 0) return 0xFFEAA7;
            return 0xDDA0DD;
        }

        // 更新位置
        function updatePositions() {
            // 更新节点位置
            nodes.forEach(node => {
                const graphic = nodeGraphics.get(node.id);
                if (graphic) {
                    graphic.x = node.x;
                    graphic.y = node.y;
                }
            });

            // 更新边位置
            edges.forEach(edge => {
                const graphic = edgeGraphics.get(edge.id);
                if (graphic && edge.source && edge.target) {
                    graphic.clear();
                    graphic.lineStyle(1, 0x666666, 0.3);
                    graphic.moveTo(edge.source.x, edge.source.y);
                    graphic.lineTo(edge.target.x, edge.target.y);
                }
            });
        }

        // 更新统计
        function updateStats() {
            document.getElementById('totalNodes').textContent = nodes.length;
            document.getElementById('totalEdges').textContent = edges.length;
            document.getElementById('visibleNodes').textContent = nodes.length;
            document.getElementById('visibleEdges').textContent = edges.length;
            document.getElementById('nodeCount').textContent = nodes.length;
            document.getElementById('edgeCount').textContent = edges.length;
        }

        // 控制函数
        function togglePhysics() {
            physicsRunning = !physicsRunning;
            if (physicsRunning) {
                simulation.alpha(0.3).restart();
            } else {
                simulation.stop();
            }
        }

        function restartLayout() {
            if (simulation) {
                simulation.alpha(1).restart();
            }
        }

        function resetView() {
            if (app) {
                app.stage.position.set(0, 0);
                app.stage.scale.set(1);
            }
        }

        // 启动应用
        window.addEventListener('load', initApp);
    </script>
</body>
</html>
