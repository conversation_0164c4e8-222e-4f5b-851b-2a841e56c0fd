<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPU显存监控测试</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #0a0a0a;
            color: white;
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .panel {
            background: rgba(0, 0, 0, 0.9);
            border: 1px solid rgba(78, 205, 196, 0.3);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .title {
            color: #4ecdc4;
            font-size: 18px;
            margin-bottom: 15px;
        }
        .metric {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .metric:last-child {
            border-bottom: none;
        }
        .label {
            color: rgba(255, 255, 255, 0.8);
        }
        .value {
            color: #ffffff;
            font-weight: bold;
        }
        .good { color: #4ecdc4; }
        .medium { color: #ffeaa7; }
        .high { color: #ff6b6b; }
        .error { color: #ff6b6b; }
        .webgl-info {
            background: rgba(255, 255, 255, 0.05);
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-size: 12px;
        }
        .test-controls {
            margin-top: 20px;
        }
        .btn {
            background: #4ecdc4;
            color: #0a0a0a;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            font-weight: bold;
        }
        .btn:hover {
            background: #45b7d1;
        }
        #testCanvas {
            border: 1px solid rgba(78, 205, 196, 0.3);
            border-radius: 4px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="color: #4ecdc4; text-align: center;">GPU显存监控测试</h1>
        
        <div class="panel">
            <div class="title">📊 GPU显存使用情况</div>
            <div class="metric">
                <span class="label">估算显存使用:</span>
                <span class="value" id="gpuMemory">计算中...</span>
            </div>
            <div class="metric">
                <span class="label">节点数量:</span>
                <span class="value" id="nodeCount">0</span>
            </div>
            <div class="metric">
                <span class="label">边数量:</span>
                <span class="value" id="edgeCount">0</span>
            </div>
            <div class="metric">
                <span class="label">状态:</span>
                <span class="value" id="status">初始化中...</span>
            </div>
        </div>

        <div class="panel">
            <div class="title">🔧 WebGL信息</div>
            <div id="webglInfo" class="webgl-info">
                正在获取WebGL信息...
            </div>
        </div>

        <div class="panel">
            <div class="title">🧪 测试控制</div>
            <div class="test-controls">
                <button class="btn" onclick="addTestNodes(100)">添加100个节点</button>
                <button class="btn" onclick="addTestNodes(500)">添加500个节点</button>
                <button class="btn" onclick="clearTestNodes()">清除测试节点</button>
                <button class="btn" onclick="updateMemoryInfo()">刷新显存信息</button>
            </div>
            <canvas id="testCanvas" width="400" height="200"></canvas>
        </div>
    </div>

    <script>
        let testNodeCount = 0;
        let testEdgeCount = 0;
        let gl = null;
        let webglInfo = null;

        // 初始化WebGL上下文
        function initWebGL() {
            const canvas = document.getElementById('testCanvas');
            gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
            
            if (!gl) {
                document.getElementById('status').textContent = 'WebGL不支持';
                document.getElementById('status').className = 'value error';
                return false;
            }

            // 获取WebGL信息
            const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
            webglInfo = {
                renderer: debugInfo ? gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) : 'Unknown',
                vendor: debugInfo ? gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL) : 'Unknown',
                version: gl.getParameter(gl.VERSION),
                maxTextureSize: gl.getParameter(gl.MAX_TEXTURE_SIZE),
                maxViewportDims: gl.getParameter(gl.MAX_VIEWPORT_DIMS),
                maxVertexAttribs: gl.getParameter(gl.MAX_VERTEX_ATTRIBS),
                maxTextureUnits: gl.getParameter(gl.MAX_TEXTURE_IMAGE_UNITS)
            };

            // 显示WebGL信息
            document.getElementById('webglInfo').innerHTML = `
                <strong>渲染器:</strong> ${webglInfo.renderer}<br>
                <strong>厂商:</strong> ${webglInfo.vendor}<br>
                <strong>版本:</strong> ${webglInfo.version}<br>
                <strong>最大纹理尺寸:</strong> ${webglInfo.maxTextureSize}x${webglInfo.maxTextureSize}<br>
                <strong>最大视口:</strong> ${webglInfo.maxViewportDims[0]}x${webglInfo.maxViewportDims[1]}<br>
                <strong>最大顶点属性:</strong> ${webglInfo.maxVertexAttribs}<br>
                <strong>最大纹理单元:</strong> ${webglInfo.maxTextureUnits}
            `;

            document.getElementById('status').textContent = 'WebGL已初始化';
            document.getElementById('status').className = 'value good';
            return true;
        }

        // 估算GPU显存使用量
        function estimateGPUMemory() {
            if (!gl || !webglInfo) return 0;

            let estimatedMemory = 0;
            
            // 基础Pixi.js应用占用
            const basePixiMemory = 10 * 1024 * 1024; // 10MB基础占用
            
            // 每个节点大约使用的显存
            const memoryPerNode = 1024; // 1KB per node
            const memoryPerEdge = 512;   // 0.5KB per edge
            
            estimatedMemory = basePixiMemory + (testNodeCount * memoryPerNode) + (testEdgeCount * memoryPerEdge);
            
            // 添加纹理和缓冲区的估算
            const maxTextureMemory = Math.min(webglInfo.maxTextureSize * webglInfo.maxTextureSize * 4, 50 * 1024 * 1024);
            estimatedMemory += maxTextureMemory * 0.1; // 假设使用了10%的最大纹理内存
            
            return Math.floor(estimatedMemory);
        }

        // 格式化内存大小
        function formatMemory(bytes) {
            if (bytes === 0) return '0 B';
            
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        // 获取显存状态类名
        function getGPUMemoryClass(gpuMemory) {
            const memoryMB = gpuMemory / (1024 * 1024);
            if (memoryMB < 100) return 'good';
            if (memoryMB < 500) return 'medium';
            return 'high';
        }

        // 更新显存信息
        function updateMemoryInfo() {
            const gpuMemory = estimateGPUMemory();
            const memoryElement = document.getElementById('gpuMemory');
            
            memoryElement.textContent = formatMemory(gpuMemory);
            memoryElement.className = 'value ' + getGPUMemoryClass(gpuMemory);
            
            document.getElementById('nodeCount').textContent = testNodeCount;
            document.getElementById('edgeCount').textContent = testEdgeCount;
        }

        // 添加测试节点
        function addTestNodes(count) {
            testNodeCount += count;
            testEdgeCount += Math.floor(count * 1.5); // 假设每个节点平均有1.5条边
            updateMemoryInfo();
        }

        // 清除测试节点
        function clearTestNodes() {
            testNodeCount = 0;
            testEdgeCount = 0;
            updateMemoryInfo();
        }

        // 初始化
        window.onload = function() {
            if (initWebGL()) {
                updateMemoryInfo();
                
                // 定期更新显存信息
                setInterval(updateMemoryInfo, 1000);
            }
        };
    </script>
</body>
</html>
