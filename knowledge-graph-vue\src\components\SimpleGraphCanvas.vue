<template>
  <div class="graph-canvas" ref="canvasContainer">
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <h3>🚀 正在初始化图谱渲染...</h3>
      <p>加载 {{ nodeCount }} 个节点和 {{ edgeCount }} 条边</p>
    </div>

    <div v-if="error" class="error-overlay">
      <h3>❌ 渲染失败</h3>
      <p>{{ error }}</p>
      <button @click="retry" class="retry-btn">重试</button>
    </div>

    <!-- 性能面板 -->
    <div v-if="showPerformance && !loading" class="performance-panel">
      <div class="panel-title">性能监控</div>
      <div class="metric">
        <span>FPS:</span>
        <span class="value">{{ fps }}</span>
      </div>
      <div class="metric">
        <span>节点:</span>
        <span class="value">{{ nodeCount }}</span>
      </div>
      <div class="metric">
        <span>边:</span>
        <span class="value">{{ edgeCount }}</span>
      </div>
      <div class="metric">
        <span>物理:</span>
        <span class="value">{{ physicsRunning ? '运行' : '停止' }}</span>
      </div>
    </div>

    <!-- 控制按钮 -->
    <div v-if="!loading" class="control-buttons">
      <button @click="togglePhysics" class="control-btn">
        {{ physicsRunning ? '⏸️' : '▶️' }}
      </button>
      <button @click="resetView" class="control-btn">🎯</button>
      <button @click="togglePerformance" class="control-btn">📊</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, computed, watch } from 'vue'
import * as PIXI from 'pixi.js'
import * as d3 from 'd3'

// Props
const props = defineProps({
  nodes: {
    type: Array,
    default: () => [],
  },
  edges: {
    type: Array,
    default: () => [],
  },
})

// 响应式数据
const canvasContainer = ref(null)
const loading = ref(true)
const error = ref(null)
const showPerformance = ref(true)
const fps = ref(60)
const physicsRunning = ref(true)

// 计算属性
const nodeCount = computed(() => props.nodes.length)
const edgeCount = computed(() => props.edges.length)

// Pixi.js 和 D3 相关变量
let app = null
let simulation = null
let nodeGraphics = new Map()
let edgeGraphics = new Map()
let animationId = null

/**
 * 初始化 Pixi.js 应用
 */
async function initPixiApp() {
  try {
    // 等待 DOM 更新并检查容器
    await nextTick()

    if (!canvasContainer.value) {
      console.error('Canvas 容器引用为空')
      throw new Error('Canvas 容器未找到')
    }

    // 检查容器是否在 DOM 中
    if (!document.contains(canvasContainer.value)) {
      console.error('Canvas 容器不在 DOM 中')
      throw new Error('Canvas 容器不在 DOM 中')
    }

    console.log('Canvas 容器检查通过:', {
      element: canvasContainer.value,
      clientWidth: canvasContainer.value.clientWidth,
      clientHeight: canvasContainer.value.clientHeight,
      offsetWidth: canvasContainer.value.offsetWidth,
      offsetHeight: canvasContainer.value.offsetHeight,
    })

    console.log('Pixi.js 模块已加载', PIXI)

    // 获取容器尺寸
    const containerRect = canvasContainer.value.getBoundingClientRect()

    // 使用容器的实际尺寸，如果太小则使用最小值
    const width = Math.max(containerRect.width, 800)
    const height = Math.max(containerRect.height, 600)

    console.log('计算画布尺寸:', { width, height })

    // 创建应用
    app = new PIXI.Application({
      width: width,
      height: height,
      backgroundColor: 0x0a0a0a,
      antialias: true,
      resolution: window.devicePixelRatio || 1,
    })

    console.log('Pixi.js 应用创建成功')

    // 添加到容器 - 兼容新旧版本
    const canvas = app.canvas || app.view
    if (canvas) {
      console.log('获取到画布，准备添加到容器')
      canvasContainer.value.appendChild(canvas)
      console.log('画布已添加到容器')
    } else {
      throw new Error('无法获取 Pixi.js 画布')
    }

    // 设置交互
    app.stage.interactive = true
    app.stage.hitArea = new PIXI.Rectangle(0, 0, app.screen.width, app.screen.height)

    console.log('Pixi.js 初始化成功')
    return true
  } catch (err) {
    console.error('Pixi.js 初始化失败:', err)
    error.value = `Pixi.js 初始化失败: ${err.message}`
    return false
  }
}

/**
 * 初始化 D3 力导向模拟
 */
async function initD3Simulation() {
  try {
    console.log('开始初始化 D3 力导向模拟')

    simulation = d3
      .forceSimulation(props.nodes)
      .force(
        'link',
        d3
          .forceLink(props.edges)
          .id((d) => d.id)
          .distance(50)
          .strength(0.1),
      )
      .force('charge', d3.forceManyBody().strength(-300).distanceMax(200))
      .force('center', d3.forceCenter(app.screen.width / 2, app.screen.height / 2))
      .force(
        'collision',
        d3
          .forceCollide()
          .radius((d) => Math.sqrt(d.degree || 1) * 2 + 5)
          .strength(0.7),
      )
      .alphaDecay(0.02)
      .velocityDecay(0.3)
      .on('tick', updatePositions)

    console.log('D3 力导向模拟初始化成功')
    return true
  } catch (err) {
    console.error('D3 初始化失败:', err)
    error.value = `D3 初始化失败: ${err.message}`
    return false
  }
}

/**
 * 创建图形对象
 */
function createGraphics() {
  if (!app) return

  // 清除现有图形
  nodeGraphics.clear()
  edgeGraphics.clear()
  app.stage.removeChildren()

  // 创建边
  props.edges.forEach((edge) => {
    const line = new PIXI.Graphics()
    edgeGraphics.set(edge.id, line)
    app.stage.addChild(line)
  })

  // 创建节点
  props.nodes.forEach((node) => {
    const circle = new PIXI.Graphics()
    const radius = Math.sqrt(node.degree || 1) * 2 + 5
    const color = getNodeColor(node.degree || 0)

    // 绘制节点
    circle.beginFill(color, 0.8)
    circle.drawCircle(0, 0, radius)
    circle.endFill()

    // 重要节点添加边框
    if ((node.degree || 0) > 10) {
      circle.lineStyle(2, 0xffffff, 0.8)
      circle.drawCircle(0, 0, radius)
    }

    // 设置交互
    circle.interactive = true
    circle.buttonMode = true
    circle.on('pointerdown', () => handleNodeClick(node))
    circle.on('pointerover', () => handleNodeHover(node))
    circle.on('pointerout', () => handleNodeOut(node))

    nodeGraphics.set(node.id, circle)
    app.stage.addChild(circle)
  })

  console.log('图形创建完成:', { nodes: nodeGraphics.size, edges: edgeGraphics.size })
}

/**
 * 获取节点颜色
 */
function getNodeColor(degree) {
  if (degree > 20) return 0xff6b6b // 超级节点
  if (degree > 10) return 0x4ecdc4 // 重要节点
  if (degree > 5) return 0x45b7d1 // 活跃节点
  if (degree > 2) return 0x96ceb4 // 普通节点
  if (degree > 0) return 0xffeaa7 // 边缘节点
  return 0xdda0dd // 孤立节点
}

/**
 * 更新位置
 */
function updatePositions() {
  // 更新节点位置
  props.nodes.forEach((node) => {
    const graphic = nodeGraphics.get(node.id)
    if (graphic && node.x !== undefined && node.y !== undefined) {
      graphic.x = node.x
      graphic.y = node.y
    }
  })

  // 更新边位置
  props.edges.forEach((edge) => {
    const graphic = edgeGraphics.get(edge.id)
    if (graphic && edge.source && edge.target) {
      graphic.clear()
      graphic.lineStyle(1, 0x666666, 0.3)
      graphic.moveTo(edge.source.x || 0, edge.source.y || 0)
      graphic.lineTo(edge.target.x || 0, edge.target.y || 0)
    }
  })
}

/**
 * 节点事件处理
 */
function handleNodeClick(node) {
  console.log('节点点击:', node.title || node.id)
}

function handleNodeHover(node) {
  console.log('节点悬停:', node.title || node.id)
}

function handleNodeOut(node) {
  // 节点移出处理
}

/**
 * 控制函数
 */
function togglePhysics() {
  physicsRunning.value = !physicsRunning.value
  if (simulation) {
    if (physicsRunning.value) {
      simulation.alpha(0.3).restart()
    } else {
      simulation.stop()
    }
  }
}

function resetView() {
  if (app) {
    app.stage.position.set(0, 0)
    app.stage.scale.set(1)
  }
}

function togglePerformance() {
  showPerformance.value = !showPerformance.value
}

function retry() {
  error.value = null
  loading.value = true
  initializeGraph()
}

/**
 * 性能监控
 */
function startPerformanceMonitoring() {
  let lastTime = performance.now()
  let frameCount = 0

  function updateFPS() {
    frameCount++
    const currentTime = performance.now()

    if (currentTime - lastTime >= 1000) {
      fps.value = Math.round((frameCount * 1000) / (currentTime - lastTime))
      frameCount = 0
      lastTime = currentTime
    }

    animationId = requestAnimationFrame(updateFPS)
  }

  updateFPS()
}

/**
 * 初始化图谱
 */
async function initializeGraph() {
  try {
    loading.value = true
    error.value = null

    console.log('开始初始化图谱...')

    // 等待 DOM 完全准备好
    await nextTick()

    // 再次等待一个微任务，确保所有 DOM 更新完成
    await new Promise((resolve) => setTimeout(resolve, 50))

    console.log('DOM 准备检查完成，开始初始化 Pixi.js')

    // 初始化 Pixi.js
    const pixiSuccess = await initPixiApp()
    if (!pixiSuccess) {
      console.error('Pixi.js 初始化失败，停止初始化')
      return
    }

    console.log('Pixi.js 初始化成功，开始初始化 D3')

    // 初始化 D3
    const d3Success = await initD3Simulation()
    if (!d3Success) {
      console.error('D3 初始化失败，停止初始化')
      return
    }

    console.log('D3 初始化成功，开始创建图形')

    // 创建图形
    createGraphics()

    console.log('图形创建完成，开始性能监控')

    // 启动性能监控
    startPerformanceMonitoring()

    loading.value = false

    console.log('图谱初始化完成')
  } catch (err) {
    console.error('图谱初始化失败:', err)
    error.value = err.message
    loading.value = false
  }
}

// 生命周期
onMounted(async () => {
  // 等待 DOM 更新
  await nextTick()

  // 不在这里初始化，让watch处理所有的初始化逻辑
  console.log('组件挂载完成，等待数据变化触发初始化')
  loading.value = false // 先关闭加载状态，等待数据
})

onUnmounted(() => {
  if (animationId) {
    cancelAnimationFrame(animationId)
  }

  if (simulation) {
    simulation.stop()
  }

  if (app) {
    app.destroy(true, true)
  }
})

// 监听数据变化
watch(
  () => [props.nodes.length, props.edges.length],
  ([newNodesLength, newEdgesLength], [oldNodesLength, oldEdgesLength] = [0, 0]) => {
    console.log('数据变化检测:', {
      newNodes: newNodesLength,
      newEdges: newEdgesLength,
      oldNodes: oldNodesLength,
      oldEdges: oldEdgesLength,
      hasApp: !!app,
    })

    // 只有在数据长度发生变化且有数据时才重新初始化
    if (
      newNodesLength > 0 &&
      newEdgesLength > 0 &&
      (newNodesLength !== oldNodesLength || newEdgesLength !== oldEdgesLength)
    ) {
      if (!app) {
        // 如果还没有初始化，现在初始化
        console.log('检测到数据，开始初始化图谱...')
        initializeGraph()
      } else {
        // 如果已经初始化，更新数据
        console.log('更新现有图谱数据...')
        updateGraphData()
      }
    }
  },
  { immediate: true },
)

// 更新图谱数据的函数
function updateGraphData() {
  if (!app || !simulation) return

  // 清除现有图形
  nodeGraphics.clear()
  edgeGraphics.clear()
  app.stage.removeChildren()

  // 重新创建图形
  createGraphics()

  // 更新模拟数据
  simulation.nodes(props.nodes)
  simulation.force('link').links(props.edges)

  // 重启模拟
  if (physicsRunning.value) {
    simulation.alpha(0.3).restart()
  }
}
</script>

<style scoped>
.graph-canvas {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f0f23 100%);
}

.graph-canvas canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100% !important;
  height: 100% !important;
}

.loading-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.8);
  z-index: 1000;
}

.loading-spinner {
  border: 3px solid rgba(78, 205, 196, 0.3);
  border-top: 3px solid #4ecdc4;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-overlay h3,
.error-overlay h3 {
  color: #4ecdc4;
  margin-bottom: 10px;
}

.loading-overlay p,
.error-overlay p {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 20px;
}

.retry-btn {
  padding: 10px 20px;
  background: #4ecdc4;
  color: #0a0a0a;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
}

.retry-btn:hover {
  background: #45b7d1;
}

.performance-panel {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(0, 0, 0, 0.9);
  border: 1px solid rgba(78, 205, 196, 0.3);
  border-radius: 8px;
  padding: 12px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  z-index: 100;
}

.panel-title {
  color: #4ecdc4;
  margin-bottom: 8px;
  font-weight: bold;
}

.metric {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  color: rgba(255, 255, 255, 0.8);
}

.metric .value {
  color: #ffffff;
  font-weight: bold;
}

.control-buttons {
  position: absolute;
  bottom: 16px;
  right: 16px;
  display: flex;
  gap: 8px;
  z-index: 100;
}

.control-btn {
  width: 40px;
  height: 40px;
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(78, 205, 196, 0.3);
  border-radius: 50%;
  color: #4ecdc4;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.control-btn:hover {
  background: rgba(78, 205, 196, 0.2);
  transform: scale(1.1);
}

/* 确保 Pixi.js 画布填满容器 */
.graph-canvas :deep(canvas) {
  display: block;
  width: 100% !important;
  height: 100% !important;
}
</style>
