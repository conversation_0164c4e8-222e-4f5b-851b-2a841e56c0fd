<script setup>
import KnowledgeGraphContainer from './components/KnowledgeGraphContainer.vue'
</script>

<template>
  <div id="app">
    <KnowledgeGraphContainer />
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f0f23 100%);
  color: #ffffff;
  overflow: hidden;
}

#app {
  height: 100vh;
  width: 100vw;
  position: relative;
  padding: 0;
  margin: 0;
}

/* 全局滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(78, 205, 196, 0.6);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(78, 205, 196, 0.8);
}

/* 工具提示样式 */
.tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  pointer-events: none;
  z-index: 1000;
  max-width: 200px;
  word-wrap: break-word;
  border: 1px solid rgba(78, 205, 196, 0.3);
}

/* 按钮基础样式 */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  outline: none;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn:active {
  transform: translateY(0);
}

.btn-primary {
  background: #4ecdc4;
  color: #0a0a0a;
}

.btn-primary:hover {
  background: #45b7d1;
}

.btn-secondary {
  background: #45b7d1;
  color: white;
}

.btn-secondary:hover {
  background: #4ecdc4;
}

.btn-warning {
  background: #ffeaa7;
  color: #0a0a0a;
}

.btn-warning:hover {
  background: #fdcb6e;
}

.btn-danger {
  background: #ff6b6b;
  color: white;
}

.btn-danger:hover {
  background: #e55656;
}

/* 输入框样式 */
.input {
  padding: 8px 12px;
  border: 1px solid rgba(78, 205, 196, 0.3);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease;
}

.input:focus {
  border-color: #4ecdc4;
  box-shadow: 0 0 0 2px rgba(78, 205, 196, 0.2);
}

.input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* 复选框样式 */
.checkbox {
  appearance: none;
  width: 16px;
  height: 16px;
  border: 1px solid rgba(78, 205, 196, 0.3);
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.1);
  cursor: pointer;
  position: relative;
  outline: none;
  transition: all 0.2s ease;
}

.checkbox:checked {
  background: #4ecdc4;
  border-color: #4ecdc4;
}

.checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: -2px;
  left: 2px;
  color: #0a0a0a;
  font-size: 12px;
  font-weight: bold;
}

/* 滑块样式 */
.slider {
  appearance: none;
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.2);
  outline: none;
  cursor: pointer;
}

.slider::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #4ecdc4;
  cursor: pointer;
  transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
  background: #45b7d1;
  transform: scale(1.1);
}

/* 加载动画 */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-spinner {
  border: 3px solid rgba(78, 205, 196, 0.3);
  border-top: 3px solid #4ecdc4;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

/* 脉冲动画 */
@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
}

.pulse {
  animation: pulse 2s ease-in-out infinite;
}
</style>
