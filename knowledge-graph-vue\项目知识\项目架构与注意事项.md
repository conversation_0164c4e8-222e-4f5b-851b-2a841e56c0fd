# 知识图谱Vue项目 - 架构与注意事项

## 🏗️ 项目架构

### 核心技术栈
- **前端框架**: Vue 3 + Composition API
- **图形渲染**: PIXI.js (WebGL渲染)
- **物理引擎**: D3.js force simulation
- **状态管理**: Pinia stores
- **构建工具**: Vite

### 主要组件结构
```
src/
├── components/
│   ├── GraphCanvas.vue          # 主图谱渲染组件
│   ├── SimpleGraphCanvas.vue    # 简化版图谱组件
│   ├── FilterControls.vue       # 筛选控制面板
│   └── HoverDetailPanel.vue     # 悬停详情面板
├── stores/
│   ├── graphData.js            # 图数据管理
│   └── uiState.js              # UI状态管理
└── utils/
    └── graphUtils.js           # 图谱工具函数
```

## 🎯 核心系统详解

### 1. 渲染系统 (PIXI.js)
**文件**: `GraphCanvas.vue`

**关键概念**:
- **容器层级**: `app.stage` > `edgesContainer` + `nodesContainer`
- **对象池**: 复用图形对象以提高性能
- **视口变换**: 支持缩放和平移

**注意事项**:
- PIXI对象需要手动管理生命周期
- 图形对象必须添加到容器才能显示
- 事件监听器需要手动清理

### 2. 物理引擎 (D3 Force)
**文件**: `GraphCanvas.vue`

**力的类型**:
- `forceLink`: 边的弹簧力
- `forceManyBody`: 节点间斥力
- `forceCenter`: 中心引力
- `forceCollide`: 碰撞检测

**注意事项**:
- 节点和边数据必须正确关联
- 模拟需要正确的启动和停止
- 力的参数影响布局效果

### 3. 数据筛选系统
**文件**: `stores/graphData.js`

**筛选维度**:
- 节点度数范围
- 节点类型
- 搜索关键词
- 最大节点数限制

**关键逻辑**:
```javascript
// 边的筛选必须基于筛选后的节点
const filteredEdges = computed(() => {
  const nodeIds = new Set(filteredNodes.value.map(node => node.id))
  return rawEdges.value.filter(edge => 
    nodeIds.has(edge.source) && nodeIds.has(edge.target)
  )
})
```

### 4. 性能优化系统

#### 视口裁剪 (Viewport Culling)
- 只渲染视口内的对象
- 可配置边界填充
- 影响节点和标签可见性

#### LOD渲染 (Level of Detail)
- 距离远的对象简化渲染
- 可调整距离阈值
- 性能模式下更激进

#### 对象池 (Object Pooling)
- 复用PIXI图形对象
- 减少GC压力
- 提高创建/销毁性能

## ⚠️ 常见陷阱与注意事项

### 1. 数据一致性问题
**问题**: 边引用了不存在的节点
```javascript
// ❌ 错误：边可能引用筛选掉的节点
const edges = rawEdges.filter(edge => someCondition)

// ✅ 正确：确保边的两端节点都存在
const nodeIds = new Set(filteredNodes.map(node => node.id))
const edges = rawEdges.filter(edge => 
  nodeIds.has(edge.source) && nodeIds.has(edge.target)
)
```

### 2. PIXI对象生命周期
**问题**: 内存泄漏和显示异常
```javascript
// ❌ 错误：没有清理事件监听器
graphic.on('click', handler)

// ✅ 正确：返回对象池前清理
graphic.removeAllListeners()
graphic.clear()
```

### 3. 多层可见性控制
**问题**: 修改一处无效，需要检查所有层级
- 视口裁剪: `graphic.visible = inViewport`
- LOD系统: `applyLOD(graphic, distance)`
- 性能模式: 影响LOD行为

### 4. 异步数据更新
**问题**: 数据更新时机不当
```javascript
// ❌ 错误：可能导致重复调用
watch(() => graphData.filteredNodes, updateGraphData)
watch(() => graphData.filteredEdges, updateGraphData)

// ✅ 正确：使用长度避免深度监听
watch(() => [
  graphData.filteredNodes.length, 
  graphData.filteredEdges.length
], updateGraphData)
```

## 🔧 调试技巧

### 1. 图形渲染调试
```javascript
// 检查图形对象状态
console.log('图形状态:', {
  visible: graphic.visible,
  alpha: graphic.alpha,
  parent: !!graphic.parent,
  bounds: graphic.getBounds()
})
```

### 2. 物理引擎调试
```javascript
// 监控模拟状态
console.log('模拟状态:', {
  alpha: simulation.alpha(),
  nodes: simulation.nodes().length,
  links: simulation.force('link').links().length
})
```

### 3. 性能监控
```javascript
// FPS监控
let frameCount = 0
let lastTime = performance.now()
function updateFPS() {
  frameCount++
  const now = performance.now()
  if (now - lastTime >= 1000) {
    console.log('FPS:', frameCount)
    frameCount = 0
    lastTime = now
  }
}
```

## 📦 依赖管理注意事项

### 包管理器使用
- **优先使用包管理器**而不是手动编辑package.json
- 避免版本冲突和依赖问题
- 使用 `npm install` / `yarn add` 等命令

### 关键依赖
- `pixi.js`: 图形渲染引擎
- `d3-force`: 物理引擎
- `vue`: 前端框架
- `pinia`: 状态管理

## 🚀 性能优化建议

### 1. 大图优化
- 限制同时显示的节点数量
- 使用LOD渲染
- 启用对象池
- 合理设置视口裁剪

### 2. 内存管理
- 及时清理PIXI对象
- 移除事件监听器
- 使用对象池复用

### 3. 渲染优化
- 避免频繁的DOM操作
- 使用requestAnimationFrame
- 合理设置更新频率

## 🔄 扩展建议

### 1. 功能扩展
- 节点分组和聚类
- 多层级图谱
- 动画过渡效果
- 导出功能

### 2. 性能扩展
- WebWorker处理大数据
- 虚拟化渲染
- 增量更新
- 缓存机制

### 3. 交互扩展
- 多选操作
- 拖拽创建边
- 右键菜单
- 快捷键支持

## 📚 学习资源

- [PIXI.js官方文档](https://pixijs.com/)
- [D3.js Force文档](https://github.com/d3/d3-force)
- [Vue 3 Composition API](https://vuejs.org/guide/composition-api-introduction.html)
- [Pinia状态管理](https://pinia.vuejs.org/)
