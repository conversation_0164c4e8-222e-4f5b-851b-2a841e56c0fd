<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue + Pixi.js 简单测试</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://pixijs.download/release/pixi.min.js"></script>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #0a0a0a;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .graph-canvas {
            width: 800px;
            height: 600px;
            border: 2px solid #4ecdc4;
            border-radius: 8px;
            background: #1a1a1a;
            position: relative;
        }
        
        .status {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            color: #4ecdc4;
            z-index: 10;
        }
        
        .error {
            color: #ff6b6b;
        }
        
        .loading {
            color: #ffd93d;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <h1>🧪 Vue + Pixi.js 简单测试</h1>
            
            <div class="graph-canvas" ref="canvasContainer">
                <div class="status" :class="{ error: hasError, loading: isLoading }">
                    {{ statusMessage }}
                </div>
            </div>
            
            <div style="margin-top: 20px; background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
                <h3>测试日志</h3>
                <div v-for="log in logs" :key="log.id" :style="{ color: log.type === 'error' ? '#ff6b6b' : '#4ecdc4', marginBottom: '5px' }">
                    {{ log.message }}
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, onMounted, nextTick } = Vue;

        createApp({
            setup() {
                const canvasContainer = ref(null);
                const statusMessage = ref('准备初始化...');
                const hasError = ref(false);
                const isLoading = ref(true);
                const logs = ref([]);
                
                let app = null;
                let logId = 0;

                function addLog(message, type = 'info') {
                    logs.value.push({
                        id: ++logId,
                        message: `[${new Date().toLocaleTimeString()}] ${message}`,
                        type
                    });
                    console.log(message);
                }

                function updateStatus(message, error = false) {
                    statusMessage.value = message;
                    hasError.value = error;
                    addLog(message, error ? 'error' : 'info');
                }

                async function testPixiVue() {
                    try {
                        updateStatus('开始测试...');
                        isLoading.value = true;

                        // 等待 DOM 准备
                        await nextTick();
                        await new Promise(resolve => setTimeout(resolve, 100));

                        addLog('DOM 准备完成');

                        // 检查依赖
                        if (typeof PIXI === 'undefined') {
                            throw new Error('PIXI 未定义');
                        }
                        addLog('✅ PIXI 可用');

                        if (typeof d3 === 'undefined') {
                            throw new Error('D3 未定义');
                        }
                        addLog('✅ D3 可用');

                        // 检查容器
                        if (!canvasContainer.value) {
                            throw new Error('Canvas 容器未找到');
                        }
                        addLog('✅ Canvas 容器找到');

                        // 检查容器是否在 DOM 中
                        if (!document.contains(canvasContainer.value)) {
                            throw new Error('Canvas 容器不在 DOM 中');
                        }
                        addLog('✅ Canvas 容器在 DOM 中');

                        updateStatus('创建 Pixi.js 应用...');

                        // 获取容器尺寸
                        const rect = canvasContainer.value.getBoundingClientRect();
                        const width = Math.max(rect.width, 760);
                        const height = Math.max(rect.height, 560);

                        addLog(`容器尺寸: ${width}x${height}`);

                        // 创建 Pixi 应用
                        app = new PIXI.Application({
                            width: width,
                            height: height,
                            backgroundColor: 0x1a1a1a,
                            antialias: true
                        });

                        addLog('✅ Pixi.js 应用创建成功');

                        // 获取画布
                        const canvas = app.canvas || app.view;
                        if (!canvas) {
                            throw new Error('无法获取画布');
                        }

                        addLog('✅ 画布获取成功');

                        // 添加到容器
                        canvasContainer.value.appendChild(canvas);
                        addLog('✅ 画布添加到容器');

                        updateStatus('创建测试图形...');

                        // 创建一些测试图形
                        const graphics = new PIXI.Graphics();
                        
                        // 中心圆
                        graphics.beginFill(0x4ecdc4);
                        graphics.drawCircle(width / 2, height / 2, 50);
                        graphics.endFill();
                        
                        // 一些随机点
                        for (let i = 0; i < 10; i++) {
                            const x = Math.random() * width;
                            const y = Math.random() * height;
                            const color = Math.random() * 0xffffff;
                            
                            graphics.beginFill(color, 0.8);
                            graphics.drawCircle(x, y, 10);
                            graphics.endFill();
                        }
                        
                        app.stage.addChild(graphics);
                        addLog('✅ 测试图形创建完成');

                        // 添加文本
                        const text = new PIXI.Text('Vue + Pixi.js 测试成功！', {
                            fontFamily: 'Arial',
                            fontSize: 24,
                            fill: 0xffffff
                        });
                        text.x = width / 2 - text.width / 2;
                        text.y = 50;
                        app.stage.addChild(text);

                        addLog('✅ 文本添加完成');

                        // 简单动画
                        let rotation = 0;
                        app.ticker.add(() => {
                            rotation += 0.01;
                            graphics.rotation = rotation;
                        });

                        addLog('✅ 动画启动');

                        updateStatus('✅ 测试完成！');
                        isLoading.value = false;

                    } catch (error) {
                        updateStatus(`❌ 测试失败: ${error.message}`, true);
                        isLoading.value = false;
                        console.error('详细错误:', error);
                    }
                }

                onMounted(() => {
                    addLog('组件挂载完成');
                    testPixiVue();
                });

                return {
                    canvasContainer,
                    statusMessage,
                    hasError,
                    isLoading,
                    logs
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
