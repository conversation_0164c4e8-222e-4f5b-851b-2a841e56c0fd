<template>
  <div
    v-if="uiState.showHoverDetailPanel"
    class="hover-detail-panel"
    :class="{ show: uiState.showHoverDetailPanel }"
  >
    <div class="panel-header">
      <h3 class="panel-title">详情面板</h3>
    </div>

    <div class="panel-content">
      <!-- 节点详情 -->
      <div
        v-if="uiState.hoverDetailPanelType === 'node' && uiState.hoverDetailPanelData"
        class="node-details"
      >
        <div class="detail-section">
          <div class="detail-item">
            <span class="detail-label">ID:</span>
            <span class="detail-value">{{ uiState.hoverDetailPanelData.id }}</span>
          </div>

          <div class="detail-item">
            <span class="detail-label">Title:</span>
            <span class="detail-value">{{ uiState.hoverDetailPanelData.title || '无标题' }}</span>
          </div>

          <div class="detail-item">
            <span class="detail-label">Type:</span>
            <span
              class="detail-value type-badge"
              :style="{ backgroundColor: getTypeColor(uiState.hoverDetailPanelData.type) }"
            >
              {{ uiState.hoverDetailPanelData.type || '未分类' }}
            </span>
          </div>

          <div class="detail-item">
            <span class="detail-label">Degree:</span>
            <span
              class="detail-value degree-value"
              :style="{ color: getDegreeColor(uiState.hoverDetailPanelData.degree) }"
            >
              {{ uiState.hoverDetailPanelData.degree }}
            </span>
          </div>

          <div class="detail-item">
            <span class="detail-label">Frequency:</span>
            <span class="detail-value">{{ uiState.hoverDetailPanelData.frequency || 0 }}</span>
          </div>
        </div>

        <div v-if="uiState.hoverDetailPanelData.description" class="detail-section">
          <div class="detail-label">Description:</div>
          <div class="description-content">
            {{ uiState.hoverDetailPanelData.description }}
          </div>
        </div>
      </div>

      <!-- 边详情 -->
      <div
        v-else-if="uiState.hoverDetailPanelType === 'edge' && uiState.hoverDetailPanelData"
        class="edge-details"
      >
        <div class="detail-section">
          <div class="detail-item">
            <span class="detail-label">ID:</span>
            <span class="detail-value">{{ uiState.hoverDetailPanelData.id }}</span>
          </div>

          <div class="detail-item">
            <span class="detail-label">Source:</span>
            <span class="detail-value">{{ uiState.hoverDetailPanelData.source }}</span>
          </div>

          <div class="detail-item">
            <span class="detail-label">Target:</span>
            <span class="detail-value">{{ uiState.hoverDetailPanelData.target }}</span>
          </div>

          <div class="detail-item">
            <span class="detail-label">From:</span>
            <span class="detail-value">{{ uiState.hoverDetailPanelData.from }}</span>
          </div>

          <div class="detail-item">
            <span class="detail-label">To:</span>
            <span class="detail-value">{{ uiState.hoverDetailPanelData.to }}</span>
          </div>

          <div class="detail-item">
            <span class="detail-label">Weight:</span>
            <span class="detail-value weight-value">{{
              uiState.hoverDetailPanelData.weight || 0
            }}</span>
          </div>

          <div class="detail-item">
            <span class="detail-label">Combined Degree:</span>
            <span class="detail-value">{{
              uiState.hoverDetailPanelData.combined_degree || 0
            }}</span>
          </div>
        </div>

        <div v-if="uiState.hoverDetailPanelData.description" class="detail-section">
          <div class="detail-label">Description:</div>
          <div class="description-content">
            {{ uiState.hoverDetailPanelData.description }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useUIStateStore } from '../stores/uiState.js'

// Stores
const uiState = useUIStateStore()

/**
 * 获取类型颜色
 */
function getTypeColor(type) {
  const colors = {
    编程概念: '#FF6B6B',
    关键字: '#4ECDC4',
    语法结构: '#45B7D1',
    函数: '#96CEB4',
    方法: '#FFEAA7',
    数据结构: '#DDA0DD',
    变量: '#F8C471',
    表达式: '#85C1E9',
  }
  return colors[type] || '#666666'
}

/**
 * 获取度数颜色
 */
function getDegreeColor(degree) {
  if (degree > 20) return '#FF6B6B'
  if (degree > 10) return '#4ECDC4'
  if (degree > 5) return '#45B7D1'
  if (degree > 2) return '#96CEB4'
  if (degree > 0) return '#FFEAA7'
  return '#DDA0DD'
}
</script>

<style scoped>
.hover-detail-panel {
  position: fixed;
  top: 300px; /* 位于性能监控面板下方 */
  right: 16px;
  width: 400px;
  height: 400px;
  background: rgba(0, 0, 0, 0.9);
  border: 1px solid rgba(78, 205, 196, 0.3);
  border-radius: 12px;
  backdrop-filter: blur(15px);
  z-index: 250;
  overflow: hidden;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.3s ease;
  color: white;
}

.hover-detail-panel.show {
  opacity: 1;
  transform: translateY(0);
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid rgba(78, 205, 196, 0.2);
  background: rgba(78, 205, 196, 0.1);
}

.panel-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #4ecdc4;
}

.panel-content {
  padding: 16px;
  max-height: 340px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 16px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
  gap: 12px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 600;
  min-width: 80px;
  flex-shrink: 0;
}

.detail-value {
  font-size: 12px;
  color: #ffffff;
  text-align: right;
  word-break: break-word;
  flex: 1;
}

.type-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  color: #000000;
}

.degree-value {
  font-weight: 700;
  font-family: 'Courier New', monospace;
}

.weight-value {
  font-family: 'Courier New', monospace;
  color: #4ecdc4;
}

.description-content {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
  padding: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border-left: 3px solid #4ecdc4;
  margin-top: 8px;
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb {
  background: rgba(78, 205, 196, 0.5);
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: rgba(78, 205, 196, 0.7);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hover-detail-panel {
    width: calc(100vw - 32px);
    max-width: 400px;
    right: 16px;
  }
}

@media (max-width: 480px) {
  .hover-detail-panel {
    width: calc(100vw - 16px);
    right: 8px;
    top: 280px;
  }

  .panel-header {
    padding: 12px;
  }

  .panel-content {
    padding: 12px;
  }

  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .detail-value {
    text-align: left;
  }
}
</style>
