{"nodes": [{"id": "函数", "title": "函数", "description": "函数是组织良好、可重复使用的代码块，用于实现特定或相关联的功能。", "degree": 30, "type": "编程概念", "frequency": 7}, {"id": "变量", "title": "变量", "description": "变量用于存储数据，可以在程序中读取和修改。", "degree": 20, "type": "编程概念", "frequency": 10}, {"id": "条件语句", "title": "条件语句", "description": "条件语句根据布尔表达式的结果决定代码的执行路径。", "degree": 15, "type": "控制结构", "frequency": 6}, {"id": "循环", "title": "循环", "description": "循环允许一段代码重复执行，直到满足特定条件。", "degree": 18, "type": "控制结构", "frequency": 8}, {"id": "数组", "title": "数组", "description": "数组是一种数据结构，用于存储一组相同类型的元素。", "degree": 22, "type": "数据结构", "frequency": 5}, {"id": "字典", "title": "字典", "description": "字典存储键值对，用于快速查找和管理数据。", "degree": 16, "type": "数据结构", "frequency": 4}, {"id": "类", "title": "类", "description": "类是面向对象编程的基本单位，用于封装属性和方法。", "degree": 25, "type": "编程概念", "frequency": 7}, {"id": "对象", "title": "对象", "description": "对象是类的实例，包含具体的数据和行为。", "degree": 28, "type": "编程概念", "frequency": 6}, {"id": "模块", "title": "模块", "description": "模块是包含一组函数和类的文件，可以被导入以复用代码。", "degree": 14, "type": "编程工具", "frequency": 3}, {"id": "异常处理", "title": "异常处理", "description": "异常处理用于捕获程序运行中的错误，保证程序稳定性。", "degree": 19, "type": "控制结构", "frequency": 4}], "edges": [{"id": "e1", "source": "函数", "target": "变量", "from": "函数", "to": "变量", "description": "函数通常会使用变量来处理数据。", "weight": 2.0, "combined_degree": 50}, {"id": "e2", "source": "条件语句", "target": "循环", "from": "条件语句", "to": "循环", "description": "循环结构通常结合条件语句控制执行流程。", "weight": 1.5, "combined_degree": 33}, {"id": "e3", "source": "类", "target": "对象", "from": "类", "to": "对象", "description": "对象是类的实例，类定义对象的结构和行为。", "weight": 3.0, "combined_degree": 53}, {"id": "e4", "source": "模块", "target": "函数", "from": "模块", "to": "函数", "description": "模块中可以包含多个函数。", "weight": 1.2, "combined_degree": 44}, {"id": "e5", "source": "数组", "target": "循环", "from": "数组", "to": "循环", "description": "循环常用于遍历数组中的元素。", "weight": 1.8, "combined_degree": 40}, {"id": "e6", "source": "异常处理", "target": "函数", "from": "异常处理", "to": "函数", "description": "函数中通常包含异常处理代码以提高健壮性。", "weight": 2.2, "combined_degree": 41}, {"id": "e7", "source": "字典", "target": "变量", "from": "字典", "to": "变量", "description": "字典经常通过变量引用进行访问和修改。", "weight": 1.4, "combined_degree": 35}, {"id": "e8", "source": "类", "target": "模块", "from": "类", "to": "模块", "description": "类可以被封装在模块中，以实现模块化编程。", "weight": 1.6, "combined_degree": 39}], "metadata": {"total_nodes": 10, "total_edges": 8, "source": "GeneratedPseudoData"}}